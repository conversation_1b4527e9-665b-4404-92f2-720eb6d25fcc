#!/usr/bin/env python3
"""
Enhanced Trading Engine
Sophisticated trading system with daily profit targets, advanced analysis, and actionable insights
"""

import asyncio
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum

from config.settings import settings
from data.market_data import MarketDataProvider
from analysis.pattern_detector import PatternDetector

logger = logging.getLogger(__name__)

class TradingStrategy(Enum):
    SCALPING = "scalping"
    DAY_TRADING = "day_trading"
    SWING_TRADING = "swing_trading"
    MOMENTUM = "momentum"
    BREAKOUT = "breakout"

class MarketCondition(Enum):
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"

@dataclass
class TradingSignal:
    symbol: str
    strategy: TradingStrategy
    action: str  # BUY, SELL, HOLD
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: int
    confidence: float
    reasoning: str
    timeframe: str
    risk_reward_ratio: float
    expected_profit: float

@dataclass
class MarketAnalysis:
    condition: MarketCondition
    volatility_level: str
    sector_leaders: List[str]
    sector_laggards: List[str]
    market_sentiment: str
    key_levels: Dict[str, float]

class EnhancedTradingEngine:
    """Advanced trading engine with profit targets and sophisticated analysis"""
    
    def __init__(self, daily_profit_target: float = 500.0):
        self.daily_profit_target = daily_profit_target
        self.current_daily_pnl = 0.0
        self.trades_today = 0
        self.max_daily_trades = 15
        self.market_data = MarketDataProvider()
        self.pattern_detector = PatternDetector()
        
        # S&P 500 + Large Cap Universe
        self.trading_universe = self._build_trading_universe()
        
        # Risk management parameters
        self.max_position_size = min(daily_profit_target * 2, 2000)  # 2x daily target or $2000 max
        self.risk_per_trade = 0.015  # 1.5% risk per trade
        
        logger.info(f"Enhanced Trading Engine initialized with ${daily_profit_target} daily target")

    def _build_trading_universe(self) -> List[str]:
        """Build comprehensive trading universe: S&P 500 + 100B+ market cap stocks"""
        
        # S&P 500 symbols (major ones for now - can be expanded)
        sp500_major = [
            # Technology
            "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE",
            "CRM", "ORCL", "INTC", "AMD", "PYPL", "UBER", "SNOW", "PLTR", "RBLX",
            
            # Healthcare & Biotech
            "JNJ", "PFE", "UNH", "ABBV", "TMO", "DHR", "BMY", "MRK", "GILD", "AMGN",
            "MRNA", "BNTX", "REGN", "VRTX", "BIIB",
            
            # Financial
            "JPM", "BAC", "WFC", "GS", "MS", "C", "BLK", "AXP", "V", "MA", "COF",
            
            # Consumer & Retail
            "WMT", "HD", "PG", "KO", "PEP", "NKE", "SBUX", "MCD", "DIS", "COST",
            "TGT", "LOW", "TJX", "AMZN",
            
            # Energy & Utilities
            "XOM", "CVX", "COP", "SLB", "EOG", "PXD", "KMI", "OKE", "NEE", "DUK",
            
            # Industrial & Materials
            "BA", "CAT", "GE", "MMM", "HON", "UPS", "FDX", "LMT", "RTX", "NOC",
            "FCX", "NEM", "AA", "X",
            
            # Communication
            "T", "VZ", "CMCSA", "CHTR", "TMUS",
            
            # ETFs for market exposure
            "SPY", "QQQ", "IWM", "DIA", "XLF", "XLK", "XLE", "XLV", "XLI", "XLP"
        ]
        
        # Add crypto-related stocks
        crypto_stocks = ["COIN", "MSTR", "RIOT", "MARA", "HUT", "BITF"]
        
        # Add meme/momentum stocks
        momentum_stocks = ["GME", "AMC", "BB", "NOK", "WISH", "CLOV", "SPCE"]
        
        return list(set(sp500_major + crypto_stocks + momentum_stocks))

    async def analyze_market_conditions(self) -> MarketAnalysis:
        """Analyze overall market conditions for strategy selection"""
        try:
            # Get major indices data
            indices = ["^GSPC", "^IXIC", "^DJI", "^RUT", "^VIX"]
            market_data = {}
            
            for index in indices:
                ticker = yf.Ticker(index)
                hist = ticker.history(period="5d", interval="1h")
                if not hist.empty:
                    market_data[index] = hist
            
            # Analyze market condition
            spy_data = market_data.get("^GSPC")
            vix_data = market_data.get("^VIX")
            
            if spy_data is not None and len(spy_data) > 20:
                # Calculate market trend
                current_price = spy_data['Close'].iloc[-1]
                sma_20 = spy_data['Close'].rolling(20).mean().iloc[-1]
                price_change_5d = (current_price - spy_data['Close'].iloc[0]) / spy_data['Close'].iloc[0]
                
                # Determine market condition
                if price_change_5d > 0.02 and current_price > sma_20:
                    condition = MarketCondition.BULLISH
                elif price_change_5d < -0.02 and current_price < sma_20:
                    condition = MarketCondition.BEARISH
                else:
                    condition = MarketCondition.SIDEWAYS
                
                # Check volatility
                if vix_data is not None and len(vix_data) > 0:
                    vix_current = vix_data['Close'].iloc[-1]
                    if vix_current > 25:
                        volatility_level = "HIGH"
                        if condition == MarketCondition.SIDEWAYS:
                            condition = MarketCondition.VOLATILE
                    elif vix_current > 20:
                        volatility_level = "MODERATE"
                    else:
                        volatility_level = "LOW"
                else:
                    volatility_level = "UNKNOWN"
                
                return MarketAnalysis(
                    condition=condition,
                    volatility_level=volatility_level,
                    sector_leaders=await self._get_sector_leaders(),
                    sector_laggards=await self._get_sector_laggards(),
                    market_sentiment="BULLISH" if condition == MarketCondition.BULLISH else "BEARISH" if condition == MarketCondition.BEARISH else "NEUTRAL",
                    key_levels={
                        "SPY_Support": float(spy_data['Low'].rolling(20).min().iloc[-1]),
                        "SPY_Resistance": float(spy_data['High'].rolling(20).max().iloc[-1]),
                        "SPY_Current": float(current_price)
                    }
                )
            
        except Exception as e:
            logger.error(f"Error analyzing market conditions: {e}")
        
        # Default fallback
        return MarketAnalysis(
            condition=MarketCondition.SIDEWAYS,
            volatility_level="MODERATE",
            sector_leaders=[],
            sector_laggards=[],
            market_sentiment="NEUTRAL",
            key_levels={}
        )

    async def _get_sector_leaders(self) -> List[str]:
        """Get top performing sector ETFs"""
        sector_etfs = ["XLK", "XLV", "XLF", "XLE", "XLI", "XLP", "XLY", "XLU", "XLRE", "XLB", "XLC"]
        performance = []
        
        for etf in sector_etfs:
            try:
                ticker = yf.Ticker(etf)
                hist = ticker.history(period="5d")
                if not hist.empty and len(hist) > 1:
                    change = (hist['Close'].iloc[-1] - hist['Close'].iloc[0]) / hist['Close'].iloc[0]
                    performance.append((etf, change))
            except:
                continue
        
        # Sort by performance and return top 3
        performance.sort(key=lambda x: x[1], reverse=True)
        return [etf for etf, _ in performance[:3]]

    async def _get_sector_laggards(self) -> List[str]:
        """Get worst performing sector ETFs"""
        sector_etfs = ["XLK", "XLV", "XLF", "XLE", "XLI", "XLP", "XLY", "XLU", "XLRE", "XLB", "XLC"]
        performance = []
        
        for etf in sector_etfs:
            try:
                ticker = yf.Ticker(etf)
                hist = ticker.history(period="5d")
                if not hist.empty and len(hist) > 1:
                    change = (hist['Close'].iloc[-1] - hist['Close'].iloc[0]) / hist['Close'].iloc[0]
                    performance.append((etf, change))
            except:
                continue
        
        # Sort by performance and return bottom 3
        performance.sort(key=lambda x: x[1])
        return [etf for etf, _ in performance[:3]]

    async def scan_for_opportunities(self, market_analysis: MarketAnalysis) -> List[TradingSignal]:
        """Scan trading universe for opportunities based on market conditions"""
        signals = []

        # Limit scanning based on daily progress
        remaining_target = self.daily_profit_target - self.current_daily_pnl
        if remaining_target <= 0:
            logger.info("Daily profit target reached! No new signals generated.")
            return signals

        # Adjust universe size based on market conditions
        if market_analysis.volatility_level == "HIGH":
            scan_symbols = self.trading_universe[:50]  # Focus on liquid stocks in high vol
        elif market_analysis.condition == MarketCondition.BULLISH:
            scan_symbols = self.trading_universe[:100]  # Broader scan in bull market
        else:
            scan_symbols = self.trading_universe[:75]  # Moderate scan

        logger.info(f"Scanning {len(scan_symbols)} symbols for opportunities...")

        # Scan symbols in batches to avoid rate limiting
        batch_size = 10
        for i in range(0, len(scan_symbols), batch_size):
            batch = scan_symbols[i:i + batch_size]
            batch_signals = await self._analyze_symbol_batch(batch, market_analysis)
            signals.extend(batch_signals)

            # Small delay between batches
            await asyncio.sleep(0.5)

        # Sort signals by confidence and expected profit
        signals.sort(key=lambda x: (x.confidence * x.expected_profit), reverse=True)

        # Return top signals based on remaining trades
        max_signals = min(self.max_daily_trades - self.trades_today, 5)
        return signals[:max_signals]

    async def _analyze_symbol_batch(self, symbols: List[str], market_analysis: MarketAnalysis) -> List[TradingSignal]:
        """Analyze a batch of symbols for trading opportunities"""
        signals = []

        for symbol in symbols:
            try:
                signal = await self._analyze_single_symbol(symbol, market_analysis)
                if signal and signal.confidence > 0.6:  # Only high-confidence signals
                    signals.append(signal)
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                continue

        return signals

    async def _analyze_single_symbol(self, symbol: str, market_analysis: MarketAnalysis) -> Optional[TradingSignal]:
        """Comprehensive analysis of a single symbol"""
        try:
            # Get market data
            ticker = yf.Ticker(symbol)

            # Get multiple timeframes
            hist_1d = ticker.history(period="5d", interval="1m")  # For scalping
            hist_5m = ticker.history(period="5d", interval="5m")  # For day trading
            hist_1h = ticker.history(period="1mo", interval="1h")  # For swing trading
            hist_daily = ticker.history(period="6mo")  # For trend analysis

            if hist_daily.empty or len(hist_daily) < 50:
                return None

            # Get stock info for fundamental screening
            info = ticker.info
            market_cap = info.get('marketCap', 0)
            volume = hist_daily['Volume'].iloc[-1]
            avg_volume = hist_daily['Volume'].rolling(20).mean().iloc[-1]

            # Screen for liquidity and size
            if market_cap < 1e9 or volume < 100000 or volume < avg_volume * 0.5:
                return None

            # Detect patterns using our pattern detector
            patterns = await self.pattern_detector.analyze_symbol(hist_daily, symbol)

            if not patterns:
                return None

            # Select best pattern
            best_pattern = max(patterns, key=lambda p: p.get('confidence', 0))

            # Determine optimal strategy based on market conditions and pattern
            strategy = self._select_strategy(best_pattern, market_analysis, hist_1d, hist_5m)

            if not strategy:
                return None

            # Calculate entry, target, and stop loss
            current_price = hist_daily['Close'].iloc[-1]
            entry_price, target_price, stop_loss = self._calculate_levels(
                current_price, best_pattern, strategy, hist_daily
            )

            # Calculate position size
            position_size = self._calculate_position_size(
                entry_price, stop_loss, self.daily_profit_target - self.current_daily_pnl
            )

            if position_size <= 0:
                return None

            # Calculate expected profit and risk-reward ratio
            risk_per_share = abs(entry_price - stop_loss)
            reward_per_share = abs(target_price - entry_price)
            risk_reward_ratio = reward_per_share / risk_per_share if risk_per_share > 0 else 0
            expected_profit = position_size * reward_per_share

            # Only proceed if risk-reward is favorable
            if risk_reward_ratio < 1.5:
                return None

            # Generate reasoning
            reasoning = self._generate_reasoning(best_pattern, strategy, market_analysis, symbol)

            return TradingSignal(
                symbol=symbol,
                strategy=strategy,
                action=best_pattern.get('action', 'WATCH'),
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                position_size=position_size,
                confidence=best_pattern.get('confidence', 0.5),
                reasoning=reasoning,
                timeframe=self._get_timeframe_for_strategy(strategy),
                risk_reward_ratio=risk_reward_ratio,
                expected_profit=expected_profit
            )

        except Exception as e:
            logger.error(f"Error in single symbol analysis for {symbol}: {e}")
            return None

    def _select_strategy(self, pattern: Dict, market_analysis: MarketAnalysis,
                        hist_1d: pd.DataFrame, hist_5m: pd.DataFrame) -> Optional[TradingStrategy]:
        """Select optimal trading strategy based on pattern and market conditions"""

        pattern_type = pattern.get('pattern_type', '')

        # High volatility favors scalping
        if market_analysis.volatility_level == "HIGH":
            if 'VOLUME_SPIKE' in pattern_type or 'BREAKOUT' in pattern_type:
                return TradingStrategy.SCALPING

        # Trending markets favor momentum strategies
        if market_analysis.condition == MarketCondition.BULLISH:
            if 'MACD' in pattern_type or 'GOLDEN_CROSS' in pattern_type:
                return TradingStrategy.MOMENTUM

        # Breakout patterns favor breakout strategy
        if 'BREAKOUT' in pattern_type or 'STDDEV_ATR_ORANGE' in pattern_type:
            return TradingStrategy.BREAKOUT

        # RSI patterns favor day trading
        if 'RSI' in pattern_type:
            return TradingStrategy.DAY_TRADING

        # Default to day trading for most patterns
        return TradingStrategy.DAY_TRADING

    def _calculate_levels(self, current_price: float, pattern: Dict,
                         strategy: TradingStrategy, hist: pd.DataFrame) -> Tuple[float, float, float]:
        """Calculate entry, target, and stop loss levels"""

        action = pattern.get('action', 'WATCH')
        pattern_type = pattern.get('pattern_type', '')

        # Calculate ATR for dynamic levels
        high_low = hist['High'] - hist['Low']
        high_close = abs(hist['High'] - hist['Close'].shift())
        low_close = abs(hist['Low'] - hist['Close'].shift())
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(14).mean().iloc[-1]

        if action == 'BUY':
            entry_price = current_price

            if strategy == TradingStrategy.SCALPING:
                target_price = current_price + (atr * 0.5)  # Quick profit
                stop_loss = current_price - (atr * 0.3)     # Tight stop
            elif strategy == TradingStrategy.BREAKOUT:
                target_price = current_price + (atr * 2.0)  # Larger target
                stop_loss = current_price - (atr * 1.0)     # Reasonable stop
            else:  # Day trading, momentum
                target_price = current_price + (atr * 1.5)
                stop_loss = current_price - (atr * 0.75)

        elif action == 'SELL':
            entry_price = current_price

            if strategy == TradingStrategy.SCALPING:
                target_price = current_price - (atr * 0.5)
                stop_loss = current_price + (atr * 0.3)
            elif strategy == TradingStrategy.BREAKOUT:
                target_price = current_price - (atr * 2.0)
                stop_loss = current_price + (atr * 1.0)
            else:
                target_price = current_price - (atr * 1.5)
                stop_loss = current_price + (atr * 0.75)
        else:
            return current_price, current_price, current_price

        return entry_price, target_price, stop_loss

    def _calculate_position_size(self, entry_price: float, stop_loss: float,
                               remaining_target: float) -> int:
        """Calculate optimal position size based on risk management"""

        # Calculate risk per share
        risk_per_share = abs(entry_price - stop_loss)

        if risk_per_share <= 0:
            return 0

        # Calculate position size based on risk percentage
        account_value = 10000  # Assume $10k account for paper trading
        max_risk = account_value * self.risk_per_trade

        # Position size based on risk
        risk_based_size = int(max_risk / risk_per_share)

        # Position size based on remaining daily target
        target_based_size = int(min(self.max_position_size, remaining_target * 2) / entry_price)

        # Take the smaller of the two
        position_size = min(risk_based_size, target_based_size)

        # Ensure minimum viable position
        return max(position_size, 1) if position_size > 0 else 0

    def _generate_reasoning(self, pattern: Dict, strategy: TradingStrategy,
                          market_analysis: MarketAnalysis, symbol: str) -> str:
        """Generate detailed reasoning for the trading signal"""

        pattern_type = pattern.get('pattern_type', 'UNKNOWN')
        confidence = pattern.get('confidence', 0.5)
        action = pattern.get('action', 'WATCH')

        reasoning = f"""
🎯 **{symbol} {action} Signal - {strategy.value.upper()}**

📊 **Pattern Detected**: {pattern_type}
🔥 **Confidence**: {confidence:.1%}
📈 **Market Condition**: {market_analysis.condition.value.upper()} ({market_analysis.volatility_level} volatility)

🧠 **Analysis**:
{pattern.get('description', 'Technical pattern detected')}

💡 **Strategy Rationale**:
"""

        if strategy == TradingStrategy.SCALPING:
            reasoning += "• Quick scalp opportunity in high-volatility environment\n"
            reasoning += "• Tight stops for rapid profit capture\n"
        elif strategy == TradingStrategy.BREAKOUT:
            reasoning += "• Strong breakout pattern with volume confirmation\n"
            reasoning += "• Momentum likely to continue in breakout direction\n"
        elif strategy == TradingStrategy.MOMENTUM:
            reasoning += "• Riding the trend with momentum indicators\n"
            reasoning += "• Market conditions favor directional moves\n"
        else:
            reasoning += "• Solid day trading setup with good risk/reward\n"
            reasoning += "• Technical indicators align for profitable trade\n"

        reasoning += f"\n🎯 **Market Context**: {market_analysis.market_sentiment} sentiment"

        if market_analysis.sector_leaders:
            reasoning += f"\n📈 **Leading Sectors**: {', '.join(market_analysis.sector_leaders)}"

        return reasoning.strip()

    def _get_timeframe_for_strategy(self, strategy: TradingStrategy) -> str:
        """Get appropriate timeframe for strategy"""
        timeframes = {
            TradingStrategy.SCALPING: "1-5 minutes",
            TradingStrategy.DAY_TRADING: "15-60 minutes",
            TradingStrategy.MOMENTUM: "1-4 hours",
            TradingStrategy.BREAKOUT: "30 minutes - 2 hours",
            TradingStrategy.SWING_TRADING: "1-5 days"
        }
        return timeframes.get(strategy, "Intraday")

    async def generate_daily_plan(self) -> Dict:
        """Generate comprehensive daily trading plan"""

        # Analyze market conditions
        market_analysis = await self.analyze_market_conditions()

        # Get trading opportunities
        signals = await self.scan_for_opportunities(market_analysis)

        # Calculate plan metrics
        total_expected_profit = sum(signal.expected_profit for signal in signals)
        total_risk = sum(signal.position_size * abs(signal.entry_price - signal.stop_loss) for signal in signals)

        plan = {
            "date": datetime.now().strftime("%Y-%m-%d"),
            "daily_target": self.daily_profit_target,
            "current_pnl": self.current_daily_pnl,
            "remaining_target": self.daily_profit_target - self.current_daily_pnl,
            "market_analysis": market_analysis,
            "trading_signals": signals,
            "plan_metrics": {
                "total_signals": len(signals),
                "expected_profit": total_expected_profit,
                "total_risk": total_risk,
                "risk_reward_ratio": total_expected_profit / total_risk if total_risk > 0 else 0,
                "trades_remaining": self.max_daily_trades - self.trades_today
            },
            "execution_strategy": self._create_execution_strategy(signals, market_analysis)
        }

        return plan

    def _create_execution_strategy(self, signals: List[TradingSignal],
                                 market_analysis: MarketAnalysis) -> Dict:
        """Create execution strategy for the trading day"""

        if not signals:
            return {"message": "No high-quality signals found. Wait for better setups."}

        # Prioritize signals
        high_priority = [s for s in signals if s.confidence > 0.8]
        medium_priority = [s for s in signals if 0.6 <= s.confidence <= 0.8]

        strategy = {
            "market_open_actions": [],
            "priority_signals": [],
            "risk_management": {},
            "timing_recommendations": {}
        }

        # Market open actions
        if market_analysis.condition == MarketCondition.BULLISH:
            strategy["market_open_actions"].append("🟢 BULLISH BIAS: Look for long opportunities in leading sectors")
        elif market_analysis.condition == MarketCondition.BEARISH:
            strategy["market_open_actions"].append("🔴 BEARISH BIAS: Consider short positions and defensive plays")
        else:
            strategy["market_open_actions"].append("🟡 NEUTRAL: Wait for clear directional moves")

        # Priority signals
        for signal in high_priority[:3]:  # Top 3 high-priority signals
            strategy["priority_signals"].append({
                "symbol": signal.symbol,
                "action": signal.action,
                "entry": signal.entry_price,
                "target": signal.target_price,
                "stop": signal.stop_loss,
                "size": signal.position_size,
                "reasoning": signal.reasoning.split('\n')[0]  # First line only
            })

        # Risk management
        strategy["risk_management"] = {
            "max_risk_per_trade": f"${self.max_position_size * self.risk_per_trade:.0f}",
            "daily_loss_limit": f"${self.daily_profit_target * 0.5:.0f}",
            "position_sizing": "Dynamic based on volatility and remaining target"
        }

        # Timing recommendations
        if market_analysis.volatility_level == "HIGH":
            strategy["timing_recommendations"]["best_times"] = "First 30 minutes, last hour"
            strategy["timing_recommendations"]["avoid"] = "Lunch hour (12-1 PM)"
        else:
            strategy["timing_recommendations"]["best_times"] = "10:30 AM - 11:30 AM, 2:00 PM - 3:30 PM"
            strategy["timing_recommendations"]["avoid"] = "First 15 minutes, lunch hour"

        return strategy

    def update_daily_pnl(self, trade_pnl: float):
        """Update daily P&L tracking"""
        self.current_daily_pnl += trade_pnl
        self.trades_today += 1

        logger.info(f"Daily P&L updated: ${self.current_daily_pnl:.2f} / ${self.daily_profit_target:.2f}")

        if self.current_daily_pnl >= self.daily_profit_target:
            logger.info("🎉 DAILY PROFIT TARGET ACHIEVED! 🎉")

    def get_daily_progress(self) -> Dict:
        """Get current daily trading progress"""
        progress_pct = (self.current_daily_pnl / self.daily_profit_target) * 100

        return {
            "target": self.daily_profit_target,
            "current_pnl": self.current_daily_pnl,
            "remaining": self.daily_profit_target - self.current_daily_pnl,
            "progress_percentage": progress_pct,
            "trades_completed": self.trades_today,
            "trades_remaining": self.max_daily_trades - self.trades_today,
            "status": "TARGET_REACHED" if progress_pct >= 100 else "IN_PROGRESS"
        }
