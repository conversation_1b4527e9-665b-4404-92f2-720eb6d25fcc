#!/usr/bin/env python3
"""
Enhanced Trading Bot - Main Application
Sophisticated trading system with daily profit targets and Trump-style AI personality
"""

import asyncio
import tkinter as tk
from tkinter import scrolledtext, messagebox, ttk
import threading
import time
from datetime import datetime
from typing import Dict
import logging

from real_time_insights import RealTimeInsightsGenerator
from trump_trading_personality import TrumpTradingPersonality
from flexible_target_interface import FlexibleTargetInterface
from enhanced_trading_engine import TimeframeUnit

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_trading_bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class EnhancedTradingBotGUI:
    """Enhanced trading bot with flexible profit targets and patriotic UI"""

    def __init__(self):
        self.insights_generator = RealTimeInsightsGenerator()
        self.personality = TrumpTradingPersonality()
        self.target_interface = None

        # Create main window
        self.root = tk.Tk()
        self.root.title("🇺🇸 AMERICA FIRST TRADING BOT - FLEXIBLE PROFIT TARGETING! 🇺🇸")
        self.root.geometry("1400x900")

        # Patriotic color scheme
        self.colors = {
            'bg_primary': '#1a1a2e',      # Dark blue
            'bg_secondary': '#16213e',     # Navy blue
            'accent_red': '#dc143c',       # Crimson red
            'accent_gold': '#ffd700',      # Gold
            'text_white': '#ffffff',       # White
            'text_light': '#e6e6e6',      # Light gray
            'success_green': '#228b22',    # Forest green
            'button_blue': '#4169e1'       # Royal blue
        }

        self.root.configure(bg=self.colors['bg_primary'])

        # Initialize UI
        self.create_widgets()
        self.setup_layout()

        # Start background tasks
        self.running = True
        self.start_background_tasks()

        # Show target configuration on startup
        self.root.after(1000, self.show_target_config)

        logger.info("Enhanced Trading Bot initialized with flexible profit targeting")

    def create_widgets(self):
        """Create patriotic-themed widgets"""
        
        # Title frame with American flag colors
        self.title_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], height=80)
        
        # Main title
        self.title_label = tk.Label(
            self.title_frame,
            text="🇺🇸 AMERICA FIRST TRADING BOT 🇺🇸",
            font=("Arial", 24, "bold"),
            fg=self.colors['accent_gold'],
            bg=self.colors['bg_primary']
        )
        
        # Subtitle
        self.subtitle_label = tk.Label(
            self.title_frame,
            text="MAKING PORTFOLIOS GREAT AGAIN WITH AI-POWERED TRADING",
            font=("Arial", 12, "bold"),
            fg=self.colors['text_white'],
            bg=self.colors['bg_primary']
        )
        
        # Flexible target display
        self.target_label = tk.Label(
            self.title_frame,
            text="🎯 NO TARGET SET - Click 'SET TARGET' to begin!",
            font=("Arial", 14, "bold"),
            fg=self.colors['accent_red'],
            bg=self.colors['bg_primary']
        )
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        
        # Configure notebook style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background=self.colors['bg_primary'])
        style.configure('TNotebook.Tab', background=self.colors['bg_secondary'], 
                       foreground=self.colors['text_white'], padding=[20, 10])
        
        # Main Dashboard Tab
        self.dashboard_frame = tk.Frame(self.notebook, bg=self.colors['bg_secondary'])
        self.notebook.add(self.dashboard_frame, text="🏛️ COMMAND CENTER")
        
        # Chat interface
        self.chat_frame = tk.Frame(self.dashboard_frame, bg=self.colors['bg_secondary'])
        
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame,
            height=25,
            width=100,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_white'],
            font=('Consolas', 11),
            wrap=tk.WORD,
            insertbackground=self.colors['accent_gold']
        )
        
        # Input frame
        self.input_frame = tk.Frame(self.chat_frame, bg=self.colors['bg_secondary'])
        
        # Input entry
        self.input_entry = tk.Entry(
            self.input_frame,
            font=("Arial", 12),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_white'],
            insertbackground=self.colors['accent_gold'],
            width=70
        )
        
        # Send button
        self.send_button = tk.Button(
            self.input_frame,
            text="🚀 EXECUTE",
            command=self.process_user_input,
            font=("Arial", 12, "bold"),
            bg=self.colors['accent_red'],
            fg=self.colors['text_white'],
            width=12,
            cursor='hand2'
        )
        
        # Control buttons frame
        self.controls_frame = tk.Frame(self.dashboard_frame, bg=self.colors['bg_secondary'])
        
        # Control buttons
        self.buttons = [
            ("🎯 SET TARGET", self.show_target_config),
            ("📊 TRADING PLAN", self.generate_trading_plan),
            ("🔍 MARKET SCAN", self.run_market_scan),
            ("📈 LIVE UPDATE", self.get_live_update),
            ("🎯 ANALYZE STOCK", self.analyze_stock_prompt),
            ("💰 PROGRESS UPDATE", self.update_progress),
            ("⚡ URGENCY CHECK", self.check_urgency)
        ]
        
        self.control_buttons = []
        for text, command in self.buttons:
            btn = tk.Button(
                self.controls_frame,
                text=text,
                command=command,
                font=("Arial", 10, "bold"),
                bg=self.colors['button_blue'],
                fg=self.colors['text_white'],
                width=15,
                height=2,
                cursor='hand2'
            )
            self.control_buttons.append(btn)
        
        # Market Status Tab
        self.market_frame = tk.Frame(self.notebook, bg=self.colors['bg_secondary'])
        self.notebook.add(self.market_frame, text="📈 MARKET INTEL")
        
        # Market display
        self.market_display = scrolledtext.ScrolledText(
            self.market_frame,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_white'],
            font=('Consolas', 10),
            wrap=tk.WORD
        )
        
        # Settings Tab
        self.settings_frame = tk.Frame(self.notebook, bg=self.colors['bg_secondary'])
        self.notebook.add(self.settings_frame, text="⚙️ SETTINGS")
        
        # Bind Enter key
        self.input_entry.bind('<Return>', lambda e: self.process_user_input())

    def setup_layout(self):
        """Setup the layout"""
        
        # Title frame
        self.title_frame.pack(fill="x", padx=10, pady=10)
        self.title_label.pack()
        self.subtitle_label.pack()
        self.target_label.pack(pady=5)
        
        # Notebook
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Dashboard layout
        self.chat_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        self.chat_display.pack(fill="both", expand=True, pady=(0, 10))
        self.input_frame.pack(fill="x")
        self.input_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        self.send_button.pack(side="right")
        
        # Controls layout
        self.controls_frame.pack(side="right", fill="y", padx=10, pady=10)
        for btn in self.control_buttons:
            btn.pack(pady=5, fill="x")
        
        # Market tab layout
        self.market_display.pack(fill="both", expand=True, padx=10, pady=10)

    def add_message(self, sender: str, message: str, color: str = None):
        """Add message to chat display"""
        
        if color is None:
            color = self.colors['text_white']
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        self.chat_display.insert(tk.END, f"\n[{timestamp}] {sender}:\n", "sender")
        self.chat_display.insert(tk.END, f"{message}\n\n", "message")
        
        # Configure tags
        self.chat_display.tag_config("sender", foreground=self.colors['accent_gold'], 
                                    font=('Consolas', 11, 'bold'))
        self.chat_display.tag_config("message", foreground=color, 
                                    font=('Consolas', 11))
        
        self.chat_display.see(tk.END)

    def process_user_input(self):
        """Process user input"""
        
        user_input = self.input_entry.get().strip()
        if not user_input:
            return
        
        self.input_entry.delete(0, tk.END)
        self.add_message("🇺🇸 YOU", user_input, self.colors['text_light'])
        
        # Process in background
        def process():
            try:
                if user_input.lower().startswith('analyze '):
                    symbol = user_input[8:].strip().upper()
                    self.analyze_specific_stock(symbol)
                elif 'plan' in user_input.lower():
                    self.generate_daily_plan()
                elif 'scan' in user_input.lower():
                    self.run_market_scan()
                elif 'update' in user_input.lower():
                    self.get_live_update()
                else:
                    # General response
                    response = self.personality.get_motivational_message()
                    self.add_message("🤖 TRUMP AI", response, self.colors['accent_gold'])
            except Exception as e:
                self.add_message("❌ ERROR", f"Error processing request: {str(e)}", 
                               self.colors['accent_red'])
        
        threading.Thread(target=process, daemon=True).start()

    def show_target_config(self):
        """Show flexible target configuration interface"""
        if not self.target_interface:
            self.target_interface = FlexibleTargetInterface(self.root, self.on_target_set)

        self.target_interface.show_target_config()

    def on_target_set(self, amount: float, timeframe_value: int, timeframe_unit: TimeframeUnit):
        """Callback when new target is set"""
        try:
            # Set the target in the trading engine
            target = self.insights_generator.trading_engine.set_profit_target(
                amount, timeframe_value, timeframe_unit
            )

            # Update display
            self.update_target_display()

            # Show confirmation message
            timeframe_desc = target.get_timeframe_description()
            message = f"""
🎯 **NEW TARGET SET!** 🎯

💰 **Target**: ${amount:.2f}
⏰ **Timeframe**: {timeframe_desc}
🏁 **Deadline**: {target.end_time.strftime('%Y-%m-%d %H:%M:%S')}

🚀 **LET'S MAKE SOME TREMENDOUS PROFITS!**

The bot will now adapt its strategy based on your timeframe:
• Ultra-short targets (≤5 min): Ultra-fast scalping
• Short targets (≤30 min): High-frequency scalping
• Medium targets (≤4 hours): Day trading
• Daily targets: Full day strategies
• Long-term targets: Swing trading

Ready to DOMINATE the markets!
"""

            self.add_message("🎯 TARGET COMMANDER", message, self.colors['success_green'])

        except Exception as e:
            self.add_message("❌ ERROR", f"Error setting target: {str(e)}", self.colors['accent_red'])

    def update_target_display(self):
        """Update the target display in the title"""
        progress = self.insights_generator.trading_engine.get_target_progress()

        if progress['status'] == 'NO_TARGET':
            self.target_label.config(text="🎯 NO TARGET SET - Click 'SET TARGET' to begin!")
        else:
            target_amount = progress['target_amount']
            current_pnl = progress['current_pnl']
            progress_pct = progress['progress_percentage']
            timeframe_desc = progress['timeframe_description']
            remaining_time = progress['remaining_time_minutes']
            urgency = progress['urgency_level']

            # Color based on urgency
            if urgency == "CRITICAL":
                color = self.colors['accent_red']
                urgency_emoji = "🚨"
            elif urgency == "HIGH":
                color = '#ff6600'  # Orange
                urgency_emoji = "⚡"
            elif urgency == "MODERATE":
                color = '#ffaa00'  # Yellow-orange
                urgency_emoji = "📊"
            else:
                color = self.colors['success_green']
                urgency_emoji = "😌"

            display_text = f"{urgency_emoji} TARGET: ${current_pnl:.2f}/${target_amount:.2f} ({progress_pct:.1f}%) | {timeframe_desc} | {remaining_time:.1f}m left"

            self.target_label.config(text=display_text, fg=color)

    def generate_trading_plan(self):
        """Generate and display flexible trading plan"""

        # Check if target is set
        progress = self.insights_generator.trading_engine.get_target_progress()
        if progress['status'] == 'NO_TARGET':
            self.add_message("⚠️ WARNING", "Please set a profit target first using the 'SET TARGET' button!",
                           self.colors['accent_red'])
            return

        urgency = progress['urgency_level']
        timeframe_desc = progress['timeframe_description']

        if urgency == "CRITICAL":
            message = f"🚨 CRITICAL URGENCY! Generating IMMEDIATE action plan for {timeframe_desc}... Every second counts!"
        elif urgency == "HIGH":
            message = f"⚡ HIGH URGENCY! Creating FAST-ACTION plan for {timeframe_desc}... Time to move QUICKLY!"
        else:
            message = f"📊 Generating your TREMENDOUS trading plan for {timeframe_desc}... This is going to be HUGE!"

        self.add_message("🤖 TRUMP AI", message, self.colors['accent_gold'])

        def generate():
            try:
                # Run async function in thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                plan_data = loop.run_until_complete(
                    self.insights_generator.generate_flexible_trading_plan()
                )

                formatted_plan = plan_data['formatted_output']
                self.add_message("🎯 FLEXIBLE BATTLE PLAN", formatted_plan, self.colors['text_white'])

                # Update market tab
                self.update_market_display(plan_data)

            except Exception as e:
                self.add_message("❌ ERROR", f"Error generating plan: {str(e)}",
                               self.colors['accent_red'])
            finally:
                loop.close()

        threading.Thread(target=generate, daemon=True).start()

    def run_market_scan(self):
        """Run comprehensive market scan"""
        
        self.add_message("🤖 TRUMP AI", "Scanning the ENTIRE market for TREMENDOUS opportunities! This is going to find us some WINNERS!", 
                        self.colors['accent_gold'])
        
        def scan():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                scan_results = loop.run_until_complete(
                    self.insights_generator.scanner.comprehensive_market_scan(max_symbols=50)
                )
                
                # Format results
                output = "🔍 **MARKET SCAN RESULTS** 🔍\n\n"
                output += f"Found {len(scan_results)} high-quality opportunities!\n\n"
                
                for i, result in enumerate(scan_results[:10], 1):
                    output += f"{i}. **{result.symbol}**: {result.recommendation} "
                    output += f"(Score: {result.score:.2f}, {result.change_percent:+.2f}%)\n"
                
                self.add_message("📊 SCAN RESULTS", output, self.colors['success_green'])
                
            except Exception as e:
                self.add_message("❌ ERROR", f"Error running scan: {str(e)}", 
                               self.colors['accent_red'])
            finally:
                loop.close()
        
        threading.Thread(target=scan, daemon=True).start()

    def get_live_update(self):
        """Get live market update"""
        
        def update():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                live_update = loop.run_until_complete(
                    self.insights_generator.get_live_market_update()
                )
                
                self.add_message("📡 LIVE UPDATE", live_update, self.colors['text_white'])
                
            except Exception as e:
                self.add_message("❌ ERROR", f"Error getting update: {str(e)}", 
                               self.colors['accent_red'])
            finally:
                loop.close()
        
        threading.Thread(target=update, daemon=True).start()

    def analyze_stock_prompt(self):
        """Prompt for stock symbol to analyze"""
        
        from tkinter import simpledialog
        
        symbol = simpledialog.askstring(
            "Stock Analysis",
            "Enter stock symbol to analyze:",
            parent=self.root
        )
        
        if symbol:
            self.analyze_specific_stock(symbol.upper())

    def analyze_specific_stock(self, symbol: str):
        """Analyze specific stock"""
        
        self.add_message("🤖 TRUMP AI", f"Analyzing {symbol}... We're going to give you the BEST analysis!", 
                        self.colors['accent_gold'])
        
        def analyze():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                analysis = loop.run_until_complete(
                    self.insights_generator.analyze_specific_stock(symbol)
                )
                
                self.add_message(f"📊 {symbol} ANALYSIS", analysis, self.colors['text_white'])
                
            except Exception as e:
                self.add_message("❌ ERROR", f"Error analyzing {symbol}: {str(e)}", 
                               self.colors['accent_red'])
            finally:
                loop.close()
        
        threading.Thread(target=analyze, daemon=True).start()

    def update_progress(self):
        """Update progress display for flexible targets"""

        progress = self.insights_generator.trading_engine.get_target_progress()

        if progress['status'] == 'NO_TARGET':
            self.add_message("⚠️ WARNING", "No active target set. Use 'SET TARGET' to begin trading!",
                           self.colors['accent_red'])
            return

        # Update title display
        self.update_target_display()

        # Determine status message and color
        if progress['status'] == 'TARGET_ACHIEVED':
            status_msg = "🎉 TARGET ACHIEVED!"
            status_color = self.colors['success_green']
        elif progress['status'] == 'TARGET_EXPIRED':
            status_msg = "⏰ TARGET EXPIRED"
            status_color = self.colors['accent_red']
        elif progress['status'] == 'CRITICAL_URGENCY':
            status_msg = "🚨 CRITICAL URGENCY!"
            status_color = self.colors['accent_red']
        elif progress['status'] == 'HIGH_URGENCY':
            status_msg = "⚡ HIGH URGENCY"
            status_color = '#ff6600'
        else:
            status_msg = "💪 IN PROGRESS"
            status_color = self.colors['success_green']

        # Create detailed progress message
        message = f"""
💰 **FLEXIBLE TARGET PROGRESS** 💰

🎯 **Target**: ${progress['target_amount']:.2f} in {progress['timeframe_description']}
💵 **Current P&L**: ${progress['current_pnl']:.2f}
📊 **Progress**: {progress['progress_percentage']:.1f}%
💸 **Remaining**: ${progress['remaining_amount']:.2f}

⏰ **TIME STATUS**:
• **Elapsed**: {progress['time_elapsed_percentage']:.1f}%
• **Remaining**: {progress['remaining_time_minutes']:.1f} minutes
• **Urgency Level**: {progress['urgency_level']}

📈 **TRADING STATUS**:
• **Strategy Focus**: {progress['strategy_focus']}
• **Trades Completed**: {progress['trades_completed']}/{progress['max_trades']}
• **Required Rate**: ${progress['required_profit_rate']:.2f}/minute

🎯 **STATUS**: {status_msg}
"""

        self.add_message("💰 PROGRESS TRACKER", message, status_color)

    def check_urgency(self):
        """Check urgency level and provide recommendations"""

        progress = self.insights_generator.trading_engine.get_target_progress()

        if progress['status'] == 'NO_TARGET':
            self.add_message("⚠️ WARNING", "No active target set. Use 'SET TARGET' to begin!",
                           self.colors['accent_red'])
            return

        urgency = progress['urgency_level']
        recommendations = self.insights_generator.trading_engine.get_urgency_recommendations()

        # Format urgency message
        if urgency == "CRITICAL":
            urgency_msg = "🚨 **CRITICAL URGENCY ALERT!** 🚨"
            urgency_color = self.colors['accent_red']
        elif urgency == "HIGH":
            urgency_msg = "⚡ **HIGH URGENCY WARNING** ⚡"
            urgency_color = '#ff6600'
        elif urgency == "MODERATE":
            urgency_msg = "📊 **MODERATE URGENCY** 📊"
            urgency_color = '#ffaa00'
        else:
            urgency_msg = "😌 **LOW URGENCY - TAKE YOUR TIME** 😌"
            urgency_color = self.colors['success_green']

        message = f"""
{urgency_msg}

⏰ **TIME REMAINING**: {progress['remaining_time_minutes']:.1f} minutes
💰 **AMOUNT REMAINING**: ${progress['remaining_amount']:.2f}
📈 **REQUIRED RATE**: ${progress['required_profit_rate']:.2f} per minute

🎯 **RECOMMENDATIONS**:
"""

        for rec in recommendations:
            message += f"• {rec}\n"

        self.add_message("⚡ URGENCY COMMANDER", message, urgency_color)

    def update_market_display(self, plan_data: Dict):
        """Update market intelligence display"""
        
        try:
            market_info = f"""
🇺🇸 MARKET INTELLIGENCE REPORT 🇺🇸
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 MARKET CONDITIONS:
• Condition: {plan_data['raw_plan']['market_analysis'].condition.value.upper()}
• Volatility: {plan_data['raw_plan']['market_analysis'].volatility_level}
• Sentiment: {plan_data['raw_plan']['market_analysis'].market_sentiment}

🎯 TRADING SIGNALS: {len(plan_data['raw_plan']['trading_signals'])}

📈 TOP OPPORTUNITIES:
"""
            
            for i, result in enumerate(plan_data['scan_results'][:10], 1):
                market_info += f"{i}. {result.symbol}: {result.recommendation} ({result.score:.2f})\n"
            
            market_info += f"""

🏭 SECTOR LEADERS:
• Monthly: {', '.join(plan_data['sector_analysis']['monthly_leaders'])}
• Weekly: {', '.join(plan_data['sector_analysis']['weekly_leaders'])}

💡 AI INSIGHTS:
{plan_data['ai_insights'][:500]}...
"""
            
            self.market_display.delete(1.0, tk.END)
            self.market_display.insert(1.0, market_info)
            
        except Exception as e:
            logger.error(f"Error updating market display: {e}")

    def start_background_tasks(self):
        """Start background monitoring tasks for flexible targets"""

        def background_monitor():
            while self.running:
                try:
                    # Update progress display
                    self.root.after(0, self.update_target_display)

                    # Check if we have an active target
                    progress = self.insights_generator.trading_engine.get_target_progress()

                    if progress['status'] != 'NO_TARGET':
                        # Adaptive update frequency based on urgency
                        urgency = progress['urgency_level']

                        if urgency == "CRITICAL":
                            sleep_time = 5  # Update every 5 seconds
                        elif urgency == "HIGH":
                            sleep_time = 15  # Update every 15 seconds
                        elif urgency == "MODERATE":
                            sleep_time = 30  # Update every 30 seconds
                        else:
                            sleep_time = 60  # Update every minute

                        # Check for target expiration or achievement
                        if progress['is_expired'] and not hasattr(self, '_expiration_notified'):
                            self.root.after(0, lambda: self.add_message(
                                "⏰ TARGET EXPIRED",
                                "Your target timeframe has expired! Set a new target to continue trading.",
                                self.colors['accent_red']
                            ))
                            self._expiration_notified = True

                        if progress['is_achieved'] and not hasattr(self, '_achievement_notified'):
                            self.root.after(0, lambda: self.add_message(
                                "🎉 TARGET ACHIEVED",
                                f"TREMENDOUS! You've achieved your ${progress['target_amount']:.2f} target! Set a new target to keep winning!",
                                self.colors['success_green']
                            ))
                            self._achievement_notified = True

                        time.sleep(sleep_time)
                    else:
                        time.sleep(30)  # Default when no target

                except Exception as e:
                    logger.error(f"Background task error: {e}")
                    time.sleep(30)

        threading.Thread(target=background_monitor, daemon=True).start()

    def run(self):
        """Run the application"""
        
        # Show welcome message
        welcome = self.personality.generate_welcome_message()
        self.add_message("🤖 TRUMP AI", welcome, self.colors['accent_gold'])
        
        # Start the GUI
        try:
            self.root.mainloop()
        finally:
            self.running = False

def main():
    """Main entry point"""

    print("🇺🇸 Starting America First Trading Bot...")
    print("🚀 Making Portfolios Great Again with FLEXIBLE PROFIT TARGETING!")
    print("💰 Set any profit target with any timeframe - from minutes to months!")

    # Launch the enhanced trading bot with flexible targeting
    app = EnhancedTradingBotGUI()
    app.run()

if __name__ == "__main__":
    main()
