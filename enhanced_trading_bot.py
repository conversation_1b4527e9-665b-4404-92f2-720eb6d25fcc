#!/usr/bin/env python3
"""
Enhanced Trading Bot - Main Application
Sophisticated trading system with daily profit targets and Trump-style AI personality
"""

import asyncio
import tkinter as tk
from tkinter import scrolledtext, messagebox, ttk
import threading
import time
from datetime import datetime
import logging
import json

from real_time_insights import RealTimeInsightsGenerator
from trump_trading_personality import TrumpTradingPersonality
from config.settings import settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_trading_bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class EnhancedTradingBotGUI:
    """Enhanced trading bot with patriotic UI and sophisticated analysis"""
    
    def __init__(self, daily_profit_target: float = 500.0):
        self.daily_profit_target = daily_profit_target
        self.insights_generator = RealTimeInsightsGenerator(daily_profit_target)
        self.personality = TrumpTradingPersonality()
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("🇺🇸 AMERICA FIRST TRADING BOT - MAKE PORTFOLIOS GREAT AGAIN! 🇺🇸")
        self.root.geometry("1400x900")
        
        # Patriotic color scheme
        self.colors = {
            'bg_primary': '#1a1a2e',      # Dark blue
            'bg_secondary': '#16213e',     # Navy blue
            'accent_red': '#dc143c',       # Crimson red
            'accent_gold': '#ffd700',      # Gold
            'text_white': '#ffffff',       # White
            'text_light': '#e6e6e6',      # Light gray
            'success_green': '#228b22',    # Forest green
            'button_blue': '#4169e1'       # Royal blue
        }
        
        self.root.configure(bg=self.colors['bg_primary'])
        
        # Initialize UI
        self.create_widgets()
        self.setup_layout()
        
        # Start background tasks
        self.running = True
        self.start_background_tasks()
        
        logger.info(f"Enhanced Trading Bot initialized with ${daily_profit_target} daily target")

    def create_widgets(self):
        """Create patriotic-themed widgets"""
        
        # Title frame with American flag colors
        self.title_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], height=80)
        
        # Main title
        self.title_label = tk.Label(
            self.title_frame,
            text="🇺🇸 AMERICA FIRST TRADING BOT 🇺🇸",
            font=("Arial", 24, "bold"),
            fg=self.colors['accent_gold'],
            bg=self.colors['bg_primary']
        )
        
        # Subtitle
        self.subtitle_label = tk.Label(
            self.title_frame,
            text="MAKING PORTFOLIOS GREAT AGAIN WITH AI-POWERED TRADING",
            font=("Arial", 12, "bold"),
            fg=self.colors['text_white'],
            bg=self.colors['bg_primary']
        )
        
        # Daily target display
        self.target_label = tk.Label(
            self.title_frame,
            text=f"🎯 DAILY TARGET: ${self.daily_profit_target:.2f} | CURRENT P&L: $0.00",
            font=("Arial", 14, "bold"),
            fg=self.colors['accent_red'],
            bg=self.colors['bg_primary']
        )
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        
        # Configure notebook style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background=self.colors['bg_primary'])
        style.configure('TNotebook.Tab', background=self.colors['bg_secondary'], 
                       foreground=self.colors['text_white'], padding=[20, 10])
        
        # Main Dashboard Tab
        self.dashboard_frame = tk.Frame(self.notebook, bg=self.colors['bg_secondary'])
        self.notebook.add(self.dashboard_frame, text="🏛️ COMMAND CENTER")
        
        # Chat interface
        self.chat_frame = tk.Frame(self.dashboard_frame, bg=self.colors['bg_secondary'])
        
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame,
            height=25,
            width=100,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_white'],
            font=('Consolas', 11),
            wrap=tk.WORD,
            insertbackground=self.colors['accent_gold']
        )
        
        # Input frame
        self.input_frame = tk.Frame(self.chat_frame, bg=self.colors['bg_secondary'])
        
        # Input entry
        self.input_entry = tk.Entry(
            self.input_frame,
            font=("Arial", 12),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_white'],
            insertbackground=self.colors['accent_gold'],
            width=70
        )
        
        # Send button
        self.send_button = tk.Button(
            self.input_frame,
            text="🚀 EXECUTE",
            command=self.process_user_input,
            font=("Arial", 12, "bold"),
            bg=self.colors['accent_red'],
            fg=self.colors['text_white'],
            width=12,
            cursor='hand2'
        )
        
        # Control buttons frame
        self.controls_frame = tk.Frame(self.dashboard_frame, bg=self.colors['bg_secondary'])
        
        # Control buttons
        self.buttons = [
            ("📊 DAILY PLAN", self.generate_daily_plan),
            ("🔍 MARKET SCAN", self.run_market_scan),
            ("📈 LIVE UPDATE", self.get_live_update),
            ("🎯 ANALYZE STOCK", self.analyze_stock_prompt),
            ("💰 P&L UPDATE", self.update_pnl)
        ]
        
        self.control_buttons = []
        for text, command in self.buttons:
            btn = tk.Button(
                self.controls_frame,
                text=text,
                command=command,
                font=("Arial", 10, "bold"),
                bg=self.colors['button_blue'],
                fg=self.colors['text_white'],
                width=15,
                height=2,
                cursor='hand2'
            )
            self.control_buttons.append(btn)
        
        # Market Status Tab
        self.market_frame = tk.Frame(self.notebook, bg=self.colors['bg_secondary'])
        self.notebook.add(self.market_frame, text="📈 MARKET INTEL")
        
        # Market display
        self.market_display = scrolledtext.ScrolledText(
            self.market_frame,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_white'],
            font=('Consolas', 10),
            wrap=tk.WORD
        )
        
        # Settings Tab
        self.settings_frame = tk.Frame(self.notebook, bg=self.colors['bg_secondary'])
        self.notebook.add(self.settings_frame, text="⚙️ SETTINGS")
        
        # Bind Enter key
        self.input_entry.bind('<Return>', lambda e: self.process_user_input())

    def setup_layout(self):
        """Setup the layout"""
        
        # Title frame
        self.title_frame.pack(fill="x", padx=10, pady=10)
        self.title_label.pack()
        self.subtitle_label.pack()
        self.target_label.pack(pady=5)
        
        # Notebook
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Dashboard layout
        self.chat_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        self.chat_display.pack(fill="both", expand=True, pady=(0, 10))
        self.input_frame.pack(fill="x")
        self.input_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        self.send_button.pack(side="right")
        
        # Controls layout
        self.controls_frame.pack(side="right", fill="y", padx=10, pady=10)
        for btn in self.control_buttons:
            btn.pack(pady=5, fill="x")
        
        # Market tab layout
        self.market_display.pack(fill="both", expand=True, padx=10, pady=10)

    def add_message(self, sender: str, message: str, color: str = None):
        """Add message to chat display"""
        
        if color is None:
            color = self.colors['text_white']
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        self.chat_display.insert(tk.END, f"\n[{timestamp}] {sender}:\n", "sender")
        self.chat_display.insert(tk.END, f"{message}\n\n", "message")
        
        # Configure tags
        self.chat_display.tag_config("sender", foreground=self.colors['accent_gold'], 
                                    font=('Consolas', 11, 'bold'))
        self.chat_display.tag_config("message", foreground=color, 
                                    font=('Consolas', 11))
        
        self.chat_display.see(tk.END)

    def process_user_input(self):
        """Process user input"""
        
        user_input = self.input_entry.get().strip()
        if not user_input:
            return
        
        self.input_entry.delete(0, tk.END)
        self.add_message("🇺🇸 YOU", user_input, self.colors['text_light'])
        
        # Process in background
        def process():
            try:
                if user_input.lower().startswith('analyze '):
                    symbol = user_input[8:].strip().upper()
                    self.analyze_specific_stock(symbol)
                elif 'plan' in user_input.lower():
                    self.generate_daily_plan()
                elif 'scan' in user_input.lower():
                    self.run_market_scan()
                elif 'update' in user_input.lower():
                    self.get_live_update()
                else:
                    # General response
                    response = self.personality.get_motivational_message()
                    self.add_message("🤖 TRUMP AI", response, self.colors['accent_gold'])
            except Exception as e:
                self.add_message("❌ ERROR", f"Error processing request: {str(e)}", 
                               self.colors['accent_red'])
        
        threading.Thread(target=process, daemon=True).start()

    def generate_daily_plan(self):
        """Generate and display daily trading plan"""
        
        self.add_message("🤖 TRUMP AI", "Generating your TREMENDOUS daily trading plan... This is going to be HUGE!", 
                        self.colors['accent_gold'])
        
        def generate():
            try:
                # Run async function in thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                plan_data = loop.run_until_complete(
                    self.insights_generator.generate_daily_trading_plan()
                )
                
                formatted_plan = plan_data['formatted_output']
                self.add_message("🎯 DAILY BATTLE PLAN", formatted_plan, self.colors['text_white'])
                
                # Update market tab
                self.update_market_display(plan_data)
                
            except Exception as e:
                self.add_message("❌ ERROR", f"Error generating plan: {str(e)}", 
                               self.colors['accent_red'])
            finally:
                loop.close()
        
        threading.Thread(target=generate, daemon=True).start()

    def run_market_scan(self):
        """Run comprehensive market scan"""
        
        self.add_message("🤖 TRUMP AI", "Scanning the ENTIRE market for TREMENDOUS opportunities! This is going to find us some WINNERS!", 
                        self.colors['accent_gold'])
        
        def scan():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                scan_results = loop.run_until_complete(
                    self.insights_generator.scanner.comprehensive_market_scan(max_symbols=50)
                )
                
                # Format results
                output = "🔍 **MARKET SCAN RESULTS** 🔍\n\n"
                output += f"Found {len(scan_results)} high-quality opportunities!\n\n"
                
                for i, result in enumerate(scan_results[:10], 1):
                    output += f"{i}. **{result.symbol}**: {result.recommendation} "
                    output += f"(Score: {result.score:.2f}, {result.change_percent:+.2f}%)\n"
                
                self.add_message("📊 SCAN RESULTS", output, self.colors['success_green'])
                
            except Exception as e:
                self.add_message("❌ ERROR", f"Error running scan: {str(e)}", 
                               self.colors['accent_red'])
            finally:
                loop.close()
        
        threading.Thread(target=scan, daemon=True).start()

    def get_live_update(self):
        """Get live market update"""
        
        def update():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                live_update = loop.run_until_complete(
                    self.insights_generator.get_live_market_update()
                )
                
                self.add_message("📡 LIVE UPDATE", live_update, self.colors['text_white'])
                
            except Exception as e:
                self.add_message("❌ ERROR", f"Error getting update: {str(e)}", 
                               self.colors['accent_red'])
            finally:
                loop.close()
        
        threading.Thread(target=update, daemon=True).start()

    def analyze_stock_prompt(self):
        """Prompt for stock symbol to analyze"""
        
        from tkinter import simpledialog
        
        symbol = simpledialog.askstring(
            "Stock Analysis",
            "Enter stock symbol to analyze:",
            parent=self.root
        )
        
        if symbol:
            self.analyze_specific_stock(symbol.upper())

    def analyze_specific_stock(self, symbol: str):
        """Analyze specific stock"""
        
        self.add_message("🤖 TRUMP AI", f"Analyzing {symbol}... We're going to give you the BEST analysis!", 
                        self.colors['accent_gold'])
        
        def analyze():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                analysis = loop.run_until_complete(
                    self.insights_generator.analyze_specific_stock(symbol)
                )
                
                self.add_message(f"📊 {symbol} ANALYSIS", analysis, self.colors['text_white'])
                
            except Exception as e:
                self.add_message("❌ ERROR", f"Error analyzing {symbol}: {str(e)}", 
                               self.colors['accent_red'])
            finally:
                loop.close()
        
        threading.Thread(target=analyze, daemon=True).start()

    def update_pnl(self):
        """Update P&L display"""
        
        current_pnl = self.insights_generator.trading_engine.current_daily_pnl
        progress = self.insights_generator.trading_engine.get_daily_progress()
        
        # Update title
        self.target_label.config(
            text=f"🎯 DAILY TARGET: ${self.daily_profit_target:.2f} | CURRENT P&L: ${current_pnl:.2f} ({progress['progress_percentage']:.1f}%)"
        )
        
        # Add message
        status = "🎉 TARGET ACHIEVED!" if progress['status'] == 'TARGET_REACHED' else "💪 WORKING TOWARDS TARGET"
        
        message = f"""
💰 **P&L UPDATE** 💰

• **Daily Target**: ${progress['target']:.2f}
• **Current P&L**: ${progress['current_pnl']:.2f}
• **Remaining**: ${progress['remaining']:.2f}
• **Progress**: {progress['progress_percentage']:.1f}%
• **Status**: {status}
• **Trades Today**: {progress['trades_completed']}/{progress['trades_completed'] + progress['trades_remaining']}
"""
        
        self.add_message("💰 P&L TRACKER", message, self.colors['success_green'])

    def update_market_display(self, plan_data: Dict):
        """Update market intelligence display"""
        
        try:
            market_info = f"""
🇺🇸 MARKET INTELLIGENCE REPORT 🇺🇸
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 MARKET CONDITIONS:
• Condition: {plan_data['raw_plan']['market_analysis'].condition.value.upper()}
• Volatility: {plan_data['raw_plan']['market_analysis'].volatility_level}
• Sentiment: {plan_data['raw_plan']['market_analysis'].market_sentiment}

🎯 TRADING SIGNALS: {len(plan_data['raw_plan']['trading_signals'])}

📈 TOP OPPORTUNITIES:
"""
            
            for i, result in enumerate(plan_data['scan_results'][:10], 1):
                market_info += f"{i}. {result.symbol}: {result.recommendation} ({result.score:.2f})\n"
            
            market_info += f"""

🏭 SECTOR LEADERS:
• Monthly: {', '.join(plan_data['sector_analysis']['monthly_leaders'])}
• Weekly: {', '.join(plan_data['sector_analysis']['weekly_leaders'])}

💡 AI INSIGHTS:
{plan_data['ai_insights'][:500]}...
"""
            
            self.market_display.delete(1.0, tk.END)
            self.market_display.insert(1.0, market_info)
            
        except Exception as e:
            logger.error(f"Error updating market display: {e}")

    def start_background_tasks(self):
        """Start background monitoring tasks"""
        
        def background_monitor():
            while self.running:
                try:
                    # Update P&L every 30 seconds
                    self.root.after(0, self.update_pnl)
                    time.sleep(30)
                except Exception as e:
                    logger.error(f"Background task error: {e}")
        
        threading.Thread(target=background_monitor, daemon=True).start()

    def run(self):
        """Run the application"""
        
        # Show welcome message
        welcome = self.personality.generate_welcome_message()
        self.add_message("🤖 TRUMP AI", welcome, self.colors['accent_gold'])
        
        # Start the GUI
        try:
            self.root.mainloop()
        finally:
            self.running = False

def main():
    """Main entry point"""
    
    print("🇺🇸 Starting America First Trading Bot...")
    print("🚀 Making Portfolios Great Again!")
    
    # Get daily profit target from user
    try:
        target = float(input("Enter your daily profit target (default $500): ") or "500")
    except ValueError:
        target = 500.0
    
    print(f"💰 Daily profit target set to: ${target:.2f}")
    
    # Launch the enhanced trading bot
    app = EnhancedTradingBotGUI(daily_profit_target=target)
    app.run()

if __name__ == "__main__":
    main()
