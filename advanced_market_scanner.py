#!/usr/bin/env python3
"""
Advanced Market Scanner
Comprehensive S&P 500 and 100B+ market cap stock scanner with real-time analysis
"""

import asyncio
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
import requests
import json

from config.settings import settings
from analysis.pattern_detector import PatternDetector

logger = logging.getLogger(__name__)

@dataclass
class ScanResult:
    symbol: str
    name: str
    price: float
    change_percent: float
    volume: int
    market_cap: float
    sector: str
    patterns: List[Dict]
    score: float
    recommendation: str
    key_metrics: Dict

class AdvancedMarketScanner:
    """Comprehensive market scanner for S&P 500 and large cap stocks"""
    
    def __init__(self):
        self.pattern_detector = PatternDetector()
        
        # Complete S&P 500 symbols (major components)
        self.sp500_symbols = [
            # Technology (Large Weight)
            "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE",
            "CRM", "ORCL", "INTC", "AMD", "PYPL", "UBER", "SNOW", "PLTR", "RBLX", "ZM",
            "SHOP", "SQ", "TWTR", "SNAP", "PINS", "DOCU", "OKTA", "CRWD", "NET", "DDOG",
            
            # Healthcare & Biotech
            "JNJ", "PFE", "UNH", "ABBV", "TMO", "DHR", "BMY", "MRK", "GILD", "AMGN",
            "MRNA", "BNTX", "REGN", "VRTX", "BIIB", "ILMN", "ISRG", "ZTS", "CVS", "CI",
            
            # Financial Services
            "JPM", "BAC", "WFC", "GS", "MS", "C", "BLK", "AXP", "V", "MA", "COF",
            "SCHW", "USB", "PNC", "TFC", "BK", "STT", "NTRS", "RF", "CFG",
            
            # Consumer Discretionary
            "AMZN", "HD", "NKE", "SBUX", "MCD", "DIS", "COST", "TGT", "LOW", "TJX",
            "BKNG", "ABNB", "EBAY", "ETSY", "W", "CHWY", "PTON", "NVR", "PHM", "LEN",
            
            # Consumer Staples
            "WMT", "PG", "KO", "PEP", "MDLZ", "CL", "KMB", "GIS", "K", "HSY",
            "CPB", "CAG", "SJM", "HRL", "MKC", "CHD", "CLX", "COST", "KR", "SYY",
            
            # Energy
            "XOM", "CVX", "COP", "SLB", "EOG", "PXD", "KMI", "OKE", "WMB", "EPD",
            "MPC", "VLO", "PSX", "HES", "DVN", "FANG", "APA", "OXY", "HAL", "BKR",
            
            # Industrials
            "BA", "CAT", "GE", "MMM", "HON", "UPS", "FDX", "LMT", "RTX", "NOC",
            "DE", "EMR", "ETN", "ITW", "PH", "CMI", "ROK", "DOV", "XYL", "IEX",
            
            # Materials
            "FCX", "NEM", "AA", "X", "CLF", "NUE", "STLD", "RS", "VMC", "MLM",
            "APD", "LIN", "ECL", "SHW", "PPG", "DD", "DOW", "LYB", "CE", "FMC",
            
            # Utilities
            "NEE", "DUK", "SO", "D", "EXC", "XEL", "SRE", "AEP", "PCG", "ED",
            "AWK", "ES", "FE", "ETR", "PPL", "CMS", "DTE", "NI", "LNT", "EVRG",
            
            # Real Estate
            "AMT", "PLD", "CCI", "EQIX", "PSA", "EXR", "AVB", "EQR", "DLR", "SBAC",
            "WY", "BXP", "VTR", "ESS", "MAA", "UDR", "CPT", "FRT", "REG", "HST",
            
            # Communication Services
            "T", "VZ", "CMCSA", "CHTR", "TMUS", "NFLX", "DIS", "GOOGL", "META", "TWTR"
        ]
        
        # Additional 100B+ market cap stocks not in S&P 500
        self.large_cap_additions = [
            "BRK-A", "BRK-B", "AVGO", "ASML", "TSM", "NVO", "SHEL", "UL", "ASML",
            "COIN", "MSTR", "RIOT", "MARA", "HUT", "BITF",  # Crypto-related
            "GME", "AMC", "BB", "NOK", "SPCE",  # Meme stocks
            "ARKK", "ARKQ", "ARKG", "ARKW", "ARKF"  # ARK ETFs
        ]
        
        # Major ETFs for market context
        self.major_etfs = [
            "SPY", "QQQ", "IWM", "DIA", "VTI", "VEA", "VWO", "EFA", "EEM",
            "XLF", "XLK", "XLE", "XLV", "XLI", "XLP", "XLY", "XLU", "XLRE", "XLB", "XLC"
        ]
        
        # Complete universe
        self.scan_universe = list(set(self.sp500_symbols + self.large_cap_additions + self.major_etfs))
        
        logger.info(f"Advanced Market Scanner initialized with {len(self.scan_universe)} symbols")

    async def comprehensive_market_scan(self, max_symbols: int = 100) -> List[ScanResult]:
        """Perform comprehensive market scan with scoring and ranking"""
        
        logger.info(f"Starting comprehensive scan of {min(max_symbols, len(self.scan_universe))} symbols...")
        
        # Prioritize symbols based on market cap and liquidity
        priority_symbols = await self._prioritize_symbols(max_symbols)
        
        # Scan in batches to avoid rate limiting
        batch_size = 20
        all_results = []
        
        for i in range(0, len(priority_symbols), batch_size):
            batch = priority_symbols[i:i + batch_size]
            logger.info(f"Scanning batch {i//batch_size + 1}: {len(batch)} symbols")
            
            batch_results = await self._scan_symbol_batch(batch)
            all_results.extend(batch_results)
            
            # Rate limiting delay
            await asyncio.sleep(1)
        
        # Filter and rank results
        filtered_results = [r for r in all_results if r.score > 0.5]
        filtered_results.sort(key=lambda x: x.score, reverse=True)
        
        logger.info(f"Scan complete: {len(filtered_results)} high-quality opportunities found")
        return filtered_results

    async def _prioritize_symbols(self, max_symbols: int) -> List[str]:
        """Prioritize symbols based on market cap, volume, and recent activity"""
        
        # Always include major indices and sector ETFs
        priority_symbols = [s for s in self.major_etfs if s in self.scan_universe]
        
        # Add high-priority individual stocks
        high_priority = [
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX",
            "JPM", "JNJ", "UNH", "HD", "PG", "V", "MA", "DIS", "PYPL", "ADBE",
            "CRM", "UBER", "COIN", "GME", "AMC"  # Include some popular/volatile stocks
        ]
        
        priority_symbols.extend([s for s in high_priority if s in self.scan_universe])
        
        # Fill remaining slots with other symbols
        remaining_slots = max_symbols - len(priority_symbols)
        other_symbols = [s for s in self.scan_universe if s not in priority_symbols]
        
        # Randomly sample from remaining to get variety
        import random
        if len(other_symbols) > remaining_slots:
            other_symbols = random.sample(other_symbols, remaining_slots)
        
        priority_symbols.extend(other_symbols)
        
        return priority_symbols[:max_symbols]

    async def _scan_symbol_batch(self, symbols: List[str]) -> List[ScanResult]:
        """Scan a batch of symbols for opportunities"""
        results = []
        
        for symbol in symbols:
            try:
                result = await self._analyze_single_symbol(symbol)
                if result:
                    results.append(result)
            except Exception as e:
                logger.error(f"Error scanning {symbol}: {e}")
                continue
        
        return results

    async def _analyze_single_symbol(self, symbol: str) -> Optional[ScanResult]:
        """Comprehensive analysis of a single symbol"""
        try:
            # Get stock data
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="3mo")  # 3 months of data
            
            if hist.empty or len(hist) < 50:
                return None
            
            # Basic metrics
            current_price = hist['Close'].iloc[-1]
            prev_close = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
            change_percent = ((current_price - prev_close) / prev_close) * 100
            
            volume = hist['Volume'].iloc[-1]
            avg_volume = hist['Volume'].rolling(20).mean().iloc[-1]
            market_cap = info.get('marketCap', 0)
            
            # Skip if insufficient liquidity or size
            if volume < 50000 or market_cap < 1e9:
                return None
            
            # Detect patterns
            patterns = await self.pattern_detector.analyze_symbol(hist, symbol)
            
            # Calculate comprehensive score
            score = self._calculate_opportunity_score(hist, patterns, info)
            
            # Generate recommendation
            recommendation = self._generate_recommendation(patterns, score, change_percent)
            
            # Key metrics
            key_metrics = self._calculate_key_metrics(hist, info)
            
            return ScanResult(
                symbol=symbol,
                name=info.get('longName', symbol),
                price=current_price,
                change_percent=change_percent,
                volume=int(volume),
                market_cap=market_cap,
                sector=info.get('sector', 'Unknown'),
                patterns=patterns,
                score=score,
                recommendation=recommendation,
                key_metrics=key_metrics
            )
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            return None

    def _calculate_opportunity_score(self, hist: pd.DataFrame, patterns: List[Dict], info: Dict) -> float:
        """Calculate comprehensive opportunity score (0-1)"""
        score = 0.0
        
        # Pattern score (40% weight)
        if patterns:
            pattern_score = max(p.get('confidence', 0) for p in patterns)
            high_value_patterns = ['BREAKOUT', 'GOLDEN_CROSS', 'STDDEV_ATR_ORANGE']
            
            for pattern in patterns:
                if any(hvp in pattern.get('pattern_type', '') for hvp in high_value_patterns):
                    pattern_score += 0.2  # Bonus for high-value patterns
            
            score += min(pattern_score, 1.0) * 0.4
        
        # Volume score (20% weight)
        current_volume = hist['Volume'].iloc[-1]
        avg_volume = hist['Volume'].rolling(20).mean().iloc[-1]
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
        
        volume_score = min(volume_ratio / 3.0, 1.0)  # Normalize to 0-1
        score += volume_score * 0.2
        
        # Momentum score (20% weight)
        returns_5d = hist['Close'].pct_change(5).iloc[-1]
        returns_20d = hist['Close'].pct_change(20).iloc[-1]
        
        momentum_score = 0.5  # Neutral baseline
        if abs(returns_5d) > 0.05:  # 5% move in 5 days
            momentum_score += 0.3
        if abs(returns_20d) > 0.15:  # 15% move in 20 days
            momentum_score += 0.2
        
        score += min(momentum_score, 1.0) * 0.2
        
        # Fundamental score (20% weight)
        market_cap = info.get('marketCap', 0)
        pe_ratio = info.get('trailingPE')
        
        fundamental_score = 0.5  # Baseline
        
        # Large cap bonus
        if market_cap > 100e9:  # 100B+
            fundamental_score += 0.3
        elif market_cap > 10e9:  # 10B+
            fundamental_score += 0.2
        
        # PE ratio consideration
        if pe_ratio and 10 <= pe_ratio <= 25:
            fundamental_score += 0.2
        
        score += min(fundamental_score, 1.0) * 0.2
        
        return min(score, 1.0)

    def _generate_recommendation(self, patterns: List[Dict], score: float, change_percent: float) -> str:
        """Generate trading recommendation based on analysis"""
        
        if score > 0.8:
            return "STRONG BUY"
        elif score > 0.7:
            return "BUY"
        elif score > 0.6:
            return "WATCH"
        elif score < 0.3:
            return "AVOID"
        else:
            return "NEUTRAL"

    def _calculate_key_metrics(self, hist: pd.DataFrame, info: Dict) -> Dict:
        """Calculate key technical and fundamental metrics"""
        
        # Technical metrics
        current_price = hist['Close'].iloc[-1]
        sma_20 = hist['Close'].rolling(20).mean().iloc[-1]
        sma_50 = hist['Close'].rolling(50).mean().iloc[-1]
        
        # RSI
        delta = hist['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1]
        
        # Volatility
        returns = hist['Close'].pct_change()
        volatility = returns.rolling(20).std().iloc[-1] * np.sqrt(252)  # Annualized
        
        return {
            'rsi': current_rsi,
            'price_vs_sma20': (current_price / sma_20 - 1) * 100,
            'price_vs_sma50': (current_price / sma_50 - 1) * 100,
            'volatility': volatility * 100,  # As percentage
            'pe_ratio': info.get('trailingPE'),
            'market_cap_b': info.get('marketCap', 0) / 1e9,  # In billions
            'beta': info.get('beta'),
            'dividend_yield': info.get('dividendYield', 0) * 100 if info.get('dividendYield') else 0
        }

    async def get_sector_analysis(self) -> Dict:
        """Analyze sector performance and rotation"""
        
        sector_etfs = {
            'Technology': 'XLK',
            'Healthcare': 'XLV', 
            'Financials': 'XLF',
            'Energy': 'XLE',
            'Industrials': 'XLI',
            'Consumer Staples': 'XLP',
            'Consumer Discretionary': 'XLY',
            'Utilities': 'XLU',
            'Real Estate': 'XLRE',
            'Materials': 'XLB',
            'Communication': 'XLC'
        }
        
        sector_performance = {}
        
        for sector, etf in sector_etfs.items():
            try:
                ticker = yf.Ticker(etf)
                hist = ticker.history(period="1mo")
                
                if not hist.empty:
                    # Calculate performance metrics
                    current = hist['Close'].iloc[-1]
                    month_start = hist['Close'].iloc[0]
                    week_start = hist['Close'].iloc[-5] if len(hist) >= 5 else month_start
                    
                    monthly_return = (current / month_start - 1) * 100
                    weekly_return = (current / week_start - 1) * 100
                    
                    sector_performance[sector] = {
                        'etf': etf,
                        'monthly_return': monthly_return,
                        'weekly_return': weekly_return,
                        'current_price': current
                    }
            except Exception as e:
                logger.error(f"Error analyzing sector {sector}: {e}")
        
        # Rank sectors
        monthly_leaders = sorted(sector_performance.items(), 
                               key=lambda x: x[1]['monthly_return'], reverse=True)
        weekly_leaders = sorted(sector_performance.items(),
                              key=lambda x: x[1]['weekly_return'], reverse=True)
        
        return {
            'sector_performance': sector_performance,
            'monthly_leaders': [s[0] for s in monthly_leaders[:3]],
            'monthly_laggards': [s[0] for s in monthly_leaders[-3:]],
            'weekly_leaders': [s[0] for s in weekly_leaders[:3]],
            'weekly_laggards': [s[0] for s in weekly_leaders[-3:]]
        }
