#!/usr/bin/env python3
"""
ChatGPT-Style Trading Bot
Real OpenAI API integration for intelligent trading conversations
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox, simpledialog, ttk
import threading
import time
from datetime import datetime, timedelta
import yfinance as yf
import requests
import json
import os
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Tuple
from dataclasses import dataclass
from enum import Enum
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

# OpenAI API Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

# Flexible Profit Targeting System
class TimeframeUnit(Enum):
    MINUTES = "minutes"
    HOURS = "hours"
    DAYS = "days"
    WEEKS = "weeks"
    MONTHS = "months"

@dataclass
class ProfitTarget:
    amount: float
    timeframe_value: int
    timeframe_unit: TimeframeUnit
    start_time: datetime
    end_time: datetime
    current_pnl: float = 0.0

    @property
    def remaining_amount(self) -> float:
        return max(0, self.amount - self.current_pnl)

    @property
    def remaining_time_seconds(self) -> float:
        return max(0, (self.end_time - datetime.now()).total_seconds())

    @property
    def remaining_time_minutes(self) -> float:
        return self.remaining_time_seconds / 60

    @property
    def progress_percentage(self) -> float:
        return min(100, (self.current_pnl / self.amount) * 100) if self.amount > 0 else 0

    @property
    def time_elapsed_percentage(self) -> float:
        total_seconds = (self.end_time - self.start_time).total_seconds()
        elapsed_seconds = (datetime.now() - self.start_time).total_seconds()
        return min(100, (elapsed_seconds / total_seconds) * 100) if total_seconds > 0 else 0

    @property
    def urgency_level(self) -> str:
        time_remaining_pct = 100 - self.time_elapsed_percentage
        progress_pct = self.progress_percentage

        if time_remaining_pct < 10 and progress_pct < 50:
            return "CRITICAL"
        elif time_remaining_pct < 25 and progress_pct < 75:
            return "HIGH"
        elif time_remaining_pct < 50:
            return "MODERATE"
        else:
            return "LOW"

    @property
    def is_expired(self) -> bool:
        return datetime.now() >= self.end_time

    @property
    def is_achieved(self) -> bool:
        return self.current_pnl >= self.amount

    def get_required_profit_rate(self) -> float:
        """Get required profit per minute to achieve target"""
        remaining_minutes = self.remaining_time_minutes
        if remaining_minutes <= 0:
            return float('inf')
        return self.remaining_amount / remaining_minutes

    def get_timeframe_description(self) -> str:
        """Get human-readable timeframe description"""
        if self.timeframe_value == 1:
            unit_name = self.timeframe_unit.value[:-1]  # Remove 's'
        else:
            unit_name = self.timeframe_unit.value

        return f"{self.timeframe_value} {unit_name}"

    def get_status_summary(self) -> str:
        """Get comprehensive status summary"""
        return f"${self.current_pnl:.2f}/${self.amount:.2f} ({self.progress_percentage:.1f}%) in {self.get_timeframe_description()}"

class FlexibleTargetManager:
    """Manages flexible profit targets"""

    def __init__(self):
        self.current_target: Optional[ProfitTarget] = None
        self.trades_completed = 0

    def set_target(self, amount: float, timeframe_value: int, timeframe_unit: TimeframeUnit) -> ProfitTarget:
        """Set a new profit target"""
        start_time = datetime.now()

        # Calculate end time based on timeframe
        if timeframe_unit == TimeframeUnit.MINUTES:
            end_time = start_time + timedelta(minutes=timeframe_value)
        elif timeframe_unit == TimeframeUnit.HOURS:
            end_time = start_time + timedelta(hours=timeframe_value)
        elif timeframe_unit == TimeframeUnit.DAYS:
            end_time = start_time + timedelta(days=timeframe_value)
        elif timeframe_unit == TimeframeUnit.WEEKS:
            end_time = start_time + timedelta(weeks=timeframe_value)
        elif timeframe_unit == TimeframeUnit.MONTHS:
            end_time = start_time + timedelta(days=timeframe_value * 30)  # Approximate
        else:
            raise ValueError(f"Unsupported timeframe unit: {timeframe_unit}")

        self.current_target = ProfitTarget(
            amount=amount,
            timeframe_value=timeframe_value,
            timeframe_unit=timeframe_unit,
            start_time=start_time,
            end_time=end_time
        )

        self.trades_completed = 0
        return self.current_target

    def update_pnl(self, trade_pnl: float):
        """Update P&L for current target"""
        if self.current_target:
            self.current_target.current_pnl += trade_pnl
            self.trades_completed += 1

    def get_progress_info(self) -> dict:
        """Get current progress information"""
        if not self.current_target:
            return {"status": "NO_TARGET", "message": "No active profit target set"}

        target = self.current_target
        return {
            "target_amount": target.amount,
            "current_pnl": target.current_pnl,
            "remaining_amount": target.remaining_amount,
            "progress_percentage": target.progress_percentage,
            "timeframe_description": target.get_timeframe_description(),
            "time_elapsed_percentage": target.time_elapsed_percentage,
            "remaining_time_minutes": target.remaining_time_minutes,
            "urgency_level": target.urgency_level,
            "required_profit_rate": target.get_required_profit_rate(),
            "trades_completed": self.trades_completed,
            "status_summary": target.get_status_summary(),
            "is_achieved": target.is_achieved,
            "is_expired": target.is_expired,
            "status": "ACTIVE"
        }

@dataclass
class TradingOpportunity:
    """Represents a specific trading opportunity with actionable details"""
    symbol: str
    company_name: str
    current_price: float
    entry_price: float
    exit_target: float
    stop_loss: float
    position_size: int
    risk_reward_ratio: float
    probability_score: float
    strategy_type: str  # "SCALP", "DAY_TRADE", "SWING"
    timeframe: str
    technical_rationale: str
    execution_method: str  # "MARKET", "LIMIT", "STOP_LIMIT"
    urgency_level: str
    sector: str
    volume_analysis: str

    @property
    def profit_potential(self) -> float:
        return (self.exit_target - self.entry_price) * self.position_size

    @property
    def risk_amount(self) -> float:
        return (self.entry_price - self.stop_loss) * self.position_size

    def get_formatted_output(self) -> str:
        return f"""
🎯 **{self.symbol} - {self.company_name}**
📊 **Current Price**: ${self.current_price:.2f}
🟢 **Entry**: ${self.entry_price:.2f}
🎯 **Target**: ${self.exit_target:.2f} (+{((self.exit_target/self.entry_price-1)*100):.1f}%)
🔴 **Stop Loss**: ${self.stop_loss:.2f} (-{((1-self.stop_loss/self.entry_price)*100):.1f}%)
📈 **Position Size**: {self.position_size} shares
💰 **Profit Potential**: ${self.profit_potential:.2f}
⚠️ **Risk Amount**: ${self.risk_amount:.2f}
📊 **Risk/Reward**: 1:{self.risk_reward_ratio:.2f}
🎲 **Success Probability**: {self.probability_score:.0f}%
⚡ **Strategy**: {self.strategy_type} ({self.timeframe})
🏭 **Sector**: {self.sector}
📋 **Execution**: {self.execution_method} order
🔍 **Technical Rationale**: {self.technical_rationale}
📊 **Volume**: {self.volume_analysis}
"""

class RealTimeMarketAnalyzer:
    """Advanced real-time market analysis engine for actionable trading insights"""

    def __init__(self):
        self.sp500_symbols = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'BRK-B', 'UNH', 'JNJ',
            'V', 'PG', 'JPM', 'HD', 'CVX', 'MA', 'ABBV', 'PFE', 'AVGO', 'KO',
            'COST', 'PEP', 'TMO', 'WMT', 'BAC', 'DIS', 'ABT', 'CRM', 'ACN', 'VZ',
            'ADBE', 'CMCSA', 'DHR', 'NKE', 'TXN', 'NEE', 'RTX', 'QCOM', 'PM', 'UPS',
            'LOW', 'ORCL', 'HON', 'T', 'IBM', 'AMGN', 'SPGI', 'CAT', 'GS', 'INTU'
        ]
        self.cache = {}
        self.cache_timeout = 60  # 1 minute cache

    def calculate_technical_indicators(self, symbol: str, period: str = "1mo") -> Dict:
        """Calculate comprehensive technical indicators for a symbol"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval="1d")

            if hist.empty:
                return {}

            # Price data
            current_price = hist['Close'].iloc[-1]

            # Moving averages
            sma_20 = hist['Close'].rolling(20).mean().iloc[-1] if len(hist) >= 20 else current_price
            sma_50 = hist['Close'].rolling(50).mean().iloc[-1] if len(hist) >= 50 else current_price
            ema_12 = hist['Close'].ewm(span=12).mean().iloc[-1]
            ema_26 = hist['Close'].ewm(span=26).mean().iloc[-1]

            # RSI calculation
            delta = hist['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs)).iloc[-1]

            # MACD
            macd = ema_12 - ema_26
            macd_signal = macd.ewm(span=9).mean().iloc[-1]
            macd_histogram = (macd - macd.ewm(span=9).mean()).iloc[-1]

            # Volume analysis
            avg_volume = hist['Volume'].rolling(20).mean().iloc[-1]
            current_volume = hist['Volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

            # Support and resistance
            high_20 = hist['High'].rolling(20).max().iloc[-1]
            low_20 = hist['Low'].rolling(20).min().iloc[-1]

            # Bollinger Bands
            bb_middle = sma_20
            bb_std = hist['Close'].rolling(20).std().iloc[-1]
            bb_upper = bb_middle + (bb_std * 2)
            bb_lower = bb_middle - (bb_std * 2)

            return {
                'current_price': current_price,
                'sma_20': sma_20,
                'sma_50': sma_50,
                'ema_12': ema_12,
                'ema_26': ema_26,
                'rsi': rsi,
                'macd': macd.iloc[-1],
                'macd_signal': macd_signal,
                'macd_histogram': macd_histogram,
                'volume_ratio': volume_ratio,
                'avg_volume': avg_volume,
                'current_volume': current_volume,
                'resistance': high_20,
                'support': low_20,
                'bb_upper': bb_upper,
                'bb_lower': bb_lower,
                'bb_middle': bb_middle
            }

        except Exception as e:
            print(f"Error calculating indicators for {symbol}: {e}")
            return {}

    def get_stock_fundamentals(self, symbol: str) -> Dict:
        """Get fundamental data for a stock"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info

            return {
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE'),
                'sector': info.get('sector', 'Unknown'),
                'industry': info.get('industry', 'Unknown'),
                'company_name': info.get('longName', symbol),
                'beta': info.get('beta'),
                'dividend_yield': info.get('dividendYield'),
                'profit_margins': info.get('profitMargins'),
                'revenue_growth': info.get('revenueGrowth')
            }
        except Exception as e:
            print(f"Error getting fundamentals for {symbol}: {e}")
            return {'company_name': symbol, 'sector': 'Unknown'}

    def generate_trading_opportunity(self, symbol: str, target_amount: float,
                                   urgency_level: str, timeframe_minutes: float) -> Optional[TradingOpportunity]:
        """Generate a specific trading opportunity based on analysis and target parameters"""
        try:
            # Get technical and fundamental data
            indicators = self.calculate_technical_indicators(symbol)
            fundamentals = self.get_stock_fundamentals(symbol)

            if not indicators:
                return None

            current_price = indicators['current_price']
            rsi = indicators['rsi']
            volume_ratio = indicators['volume_ratio']

            # Determine strategy type based on urgency and timeframe
            if urgency_level == "CRITICAL" or timeframe_minutes <= 30:
                strategy_type = "SCALP"
                timeframe_desc = "1-5 minutes"
                execution_method = "MARKET"
            elif urgency_level == "HIGH" or timeframe_minutes <= 240:
                strategy_type = "DAY_TRADE"
                timeframe_desc = "15-60 minutes"
                execution_method = "LIMIT"
            else:
                strategy_type = "SWING"
                timeframe_desc = "1-5 days"
                execution_method = "LIMIT"

            # Calculate entry, exit, and stop loss based on strategy
            if strategy_type == "SCALP":
                # Tight scalping parameters
                entry_price = current_price * 1.001  # Slight premium for immediate entry
                exit_target = current_price * 1.005  # 0.5% target
                stop_loss = current_price * 0.997   # 0.3% stop
                risk_reward = 1.67
            elif strategy_type == "DAY_TRADE":
                # Day trading parameters
                entry_price = current_price * 1.002
                exit_target = current_price * 1.015  # 1.5% target
                stop_loss = current_price * 0.992   # 0.8% stop
                risk_reward = 1.88
            else:
                # Swing trading parameters
                entry_price = current_price * 1.005
                exit_target = current_price * 1.035  # 3.5% target
                stop_loss = current_price * 0.985   # 1.5% stop
                risk_reward = 2.33

            # Calculate position size based on target amount and risk
            risk_per_share = entry_price - stop_loss
            max_risk = target_amount * 0.02  # Risk 2% of target
            position_size = max(1, int(max_risk / risk_per_share)) if risk_per_share > 0 else 100

            # Generate probability score based on technical indicators
            probability_score = self.calculate_probability_score(indicators, strategy_type)

            # Generate technical rationale
            technical_rationale = self.generate_technical_rationale(indicators, strategy_type)

            # Volume analysis
            if volume_ratio > 1.5:
                volume_analysis = f"High volume ({volume_ratio:.1f}x avg) - Strong interest"
            elif volume_ratio < 0.7:
                volume_analysis = f"Low volume ({volume_ratio:.1f}x avg) - Weak conviction"
            else:
                volume_analysis = f"Normal volume ({volume_ratio:.1f}x avg) - Steady flow"

            return TradingOpportunity(
                symbol=symbol,
                company_name=fundamentals.get('company_name', symbol),
                current_price=current_price,
                entry_price=entry_price,
                exit_target=exit_target,
                stop_loss=stop_loss,
                position_size=position_size,
                risk_reward_ratio=risk_reward,
                probability_score=probability_score,
                strategy_type=strategy_type,
                timeframe=timeframe_desc,
                technical_rationale=technical_rationale,
                execution_method=execution_method,
                urgency_level=urgency_level,
                sector=fundamentals.get('sector', 'Unknown'),
                volume_analysis=volume_analysis
            )

        except Exception as e:
            print(f"Error generating opportunity for {symbol}: {e}")
            return None

    def calculate_probability_score(self, indicators: Dict, strategy_type: str) -> float:
        """Calculate probability of success based on technical indicators"""
        score = 50.0  # Base score

        rsi = indicators.get('rsi', 50)
        volume_ratio = indicators.get('volume_ratio', 1)
        current_price = indicators.get('current_price', 0)
        sma_20 = indicators.get('sma_20', current_price)
        sma_50 = indicators.get('sma_50', current_price)

        # RSI scoring
        if 30 <= rsi <= 70:
            score += 15  # Neutral RSI is good
        elif rsi < 30:
            score += 10  # Oversold can be opportunity
        elif rsi > 70:
            score += 5   # Overbought is risky

        # Trend scoring
        if current_price > sma_20 > sma_50:
            score += 20  # Strong uptrend
        elif current_price > sma_20:
            score += 10  # Mild uptrend

        # Volume scoring
        if volume_ratio > 1.5:
            score += 15  # High volume is good
        elif volume_ratio > 1.2:
            score += 10
        elif volume_ratio < 0.8:
            score -= 10  # Low volume is concerning

        # Strategy-specific adjustments
        if strategy_type == "SCALP":
            if volume_ratio > 2.0:
                score += 10  # High volume crucial for scalping
        elif strategy_type == "SWING":
            if current_price > sma_50:
                score += 10  # Long-term trend important for swings

        return min(95, max(15, score))  # Cap between 15-95%

    def generate_technical_rationale(self, indicators: Dict, strategy_type: str) -> str:
        """Generate human-readable technical rationale"""
        rationales = []

        rsi = indicators.get('rsi', 50)
        volume_ratio = indicators.get('volume_ratio', 1)
        current_price = indicators.get('current_price', 0)
        sma_20 = indicators.get('sma_20', current_price)
        macd_histogram = indicators.get('macd_histogram', 0)

        # RSI analysis
        if rsi < 30:
            rationales.append("RSI oversold - potential bounce")
        elif rsi > 70:
            rationales.append("RSI overbought - momentum play")
        else:
            rationales.append("RSI neutral - balanced conditions")

        # Trend analysis
        if current_price > sma_20:
            rationales.append("Above 20-day MA - bullish trend")
        else:
            rationales.append("Below 20-day MA - bearish pressure")

        # MACD analysis
        if macd_histogram > 0:
            rationales.append("MACD positive - upward momentum")
        else:
            rationales.append("MACD negative - downward pressure")

        # Volume analysis
        if volume_ratio > 1.5:
            rationales.append("High volume confirms move")

        return "; ".join(rationales)

    def scan_market_opportunities(self, target_amount: float, urgency_level: str,
                                timeframe_minutes: float, max_opportunities: int = 5) -> List[TradingOpportunity]:
        """Scan the market for the best trading opportunities"""
        opportunities = []

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=10) as executor:
            # Submit tasks for top symbols
            future_to_symbol = {
                executor.submit(self.generate_trading_opportunity, symbol, target_amount,
                              urgency_level, timeframe_minutes): symbol
                for symbol in self.sp500_symbols[:30]  # Scan top 30 for speed
            }

            for future in concurrent.futures.as_completed(future_to_symbol):
                try:
                    opportunity = future.result(timeout=5)
                    if opportunity and opportunity.probability_score > 60:
                        opportunities.append(opportunity)
                except Exception as e:
                    symbol = future_to_symbol[future]
                    print(f"Error processing {symbol}: {e}")

        # Sort by probability score and return top opportunities
        opportunities.sort(key=lambda x: x.probability_score, reverse=True)
        return opportunities[:max_opportunities]

class RealTimeMarketData:
    """Get real market data for AI context"""

    def get_stock_data(self, symbol):
        """Get current stock data"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="5d")

            if hist.empty:
                return None

            current_price = info.get('currentPrice') or hist['Close'].iloc[-1]
            previous_close = info.get('previousClose') or hist['Close'].iloc[-2]
            change = current_price - previous_close
            change_percent = (change / previous_close) * 100

            return {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'current_price': current_price,
                'change': change,
                'change_percent': change_percent,
                'volume': hist['Volume'].iloc[-1],
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'sector': info.get('sector'),
                '52w_high': info.get('fiftyTwoWeekHigh'),
                '52w_low': info.get('fiftyTwoWeekLow')
            }
        except Exception as e:
            print(f"Error getting data for {symbol}: {e}")
            return None

    def get_insider_trades(self, symbol=None):
        """Get recent insider trading data"""
        try:
            if symbol:
                # Get insider trades for specific symbol
                ticker = yf.Ticker(symbol)
                insider_trades = ticker.insider_transactions

                if insider_trades is not None and not insider_trades.empty:
                    # Get recent trades (last 10)
                    recent_trades = insider_trades.head(10)

                    trades_data = []
                    for _, trade in recent_trades.iterrows():
                        trades_data.append({
                            'symbol': symbol,
                            'insider': trade.get('Insider', 'Unknown'),
                            'title': trade.get('Title', 'Unknown'),
                            'transaction': trade.get('Transaction', 'Unknown'),
                            'shares': trade.get('Shares', 0),
                            'price': trade.get('Price', 0),
                            'date': trade.get('Date', 'Unknown')
                        })

                    return trades_data
            else:
                # Get insider trades for popular stocks
                popular_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
                all_trades = []

                for stock in popular_stocks:
                    trades = self.get_insider_trades(stock)
                    if trades:
                        all_trades.extend(trades[:2])  # Get top 2 from each

                return all_trades

        except Exception as e:
            print(f"Error getting insider trades: {e}")
            return []

    def get_recent_news(self, symbol=None):
        """Get recent market news"""
        try:
            if symbol:
                ticker = yf.Ticker(symbol)
                news = ticker.news

                if news:
                    return [{
                        'title': item.get('title', ''),
                        'publisher': item.get('publisher', ''),
                        'link': item.get('link', ''),
                        'published': datetime.fromtimestamp(item.get('providerPublishTime', 0)).strftime('%Y-%m-%d %H:%M')
                    } for item in news[:5]]

            return []
        except Exception as e:
            print(f"Error getting news: {e}")
            return []
    
    def get_market_overview(self):
        """Get market indices overview"""
        indices = {
            '^GSPC': 'S&P 500',
            '^IXIC': 'NASDAQ',
            '^DJI': 'Dow Jones'
        }
        
        market_data = {}
        for symbol, name in indices.items():
            data = self.get_stock_data(symbol)
            if data:
                market_data[name] = data
        
        return market_data

class ChatGPTTradingBot:
    """ChatGPT-style trading bot with OpenAI API, flexible profit targeting, and real-time market analysis"""

    def __init__(self):
        self.market_data = RealTimeMarketData()
        self.market_analyzer = RealTimeMarketAnalyzer()
        self.conversation_history = []
        self.target_manager = FlexibleTargetManager()

        # System prompt for trading expertise with flexible targeting and real-time analysis
        self.system_prompt = """You are a professional trading and investment advisor with deep expertise in:

- Technical analysis (moving averages, RSI, MACD, support/resistance)
- Fundamental analysis (P/E ratios, earnings, financial statements)
- Market trends and sector analysis
- Risk management and position sizing
- Options trading strategies
- Cryptocurrency analysis
- Economic indicators and their market impact
- Insider trading analysis and interpretation
- FLEXIBLE PROFIT TARGETING: Helping users set and achieve profit targets with any amount and any timeframe
- REAL-TIME MARKET ANALYSIS: Providing specific, actionable trading opportunities with exact entry/exit points

You have access to REAL-TIME market data and analysis including:
- Current stock prices, volume, and technical indicators for S&P 500 stocks
- Real-time market scanning with probability scores
- Specific trading opportunities with entry/exit prices and position sizing
- Recent insider trading transactions
- Latest market news and developments
- Sector performance data
- FLEXIBLE PROFIT TARGET TRACKING: Current target progress, urgency levels, and time remaining

REAL-TIME MARKET ANALYSIS CAPABILITIES:
You can scan the entire S&P 500 and provide SPECIFIC, ACTIONABLE trading opportunities including:
- Exact stock symbols with current prices
- Precise entry and exit prices
- Calculated position sizes based on risk management
- Stop-loss levels and profit targets
- Risk/reward ratios and success probabilities
- Timeframe-specific strategies (scalping, day trading, swing trading)
- Technical rationale based on real indicators

FLEXIBLE TARGETING CAPABILITIES:
You can help users set profit targets with ANY amount ($5 to $50,000+) and ANY timeframe (5 minutes to months). You automatically adapt your trading advice based on:
- Target amount and timeframe
- Current progress toward target
- Time remaining (urgency level: CRITICAL, HIGH, MODERATE, LOW)
- Required profit rate per minute

URGENCY-BASED STRATEGY ADAPTATION:
- CRITICAL urgency (< 10% time left): Ultra-fast scalping with 1-minute charts, market orders, immediate execution
- HIGH urgency (< 25% time left): Aggressive day trading with 5-15 minute charts, quick opportunities
- MODERATE urgency (< 50% time left): Balanced day trading with 15-minute charts
- LOW urgency (> 50% time left): Patient swing trading with hourly/daily charts, high-quality setups

ACTIONABLE OUTPUT FORMAT:
When providing trading recommendations, you ALWAYS include:
- Specific stock symbol and current price
- Exact entry price and execution method (MARKET/LIMIT)
- Precise exit target and stop-loss levels
- Position size recommendation
- Risk/reward ratio and success probability
- Technical rationale based on real indicators
- Timeframe-appropriate execution strategy

You provide clear, actionable trading advice while always emphasizing risk management. When users ask about market analysis, trading opportunities, or specific stocks, you ALWAYS pull and analyze the current real-time data. Never say you don't have access to data - you do have access and should use it.

You are knowledgeable, professional, and direct. You don't just give generic advice - you provide specific, actionable insights with exact numbers, prices, and execution instructions based on current market conditions, real data, and their active profit targets."""

    def get_ai_response(self, user_message, market_context=None):
        """Get response from OpenAI API"""
        try:
            # Prepare the message with market context
            if market_context:
                enhanced_message = f"""
User Question: {user_message}

Current Market Data:
{market_context}

Please provide a comprehensive analysis considering this real-time data.
"""
            else:
                enhanced_message = user_message
            
            # Prepare conversation for API
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (last 10 messages)
            for msg in self.conversation_history[-10:]:
                messages.append(msg)
            
            # Add current message
            messages.append({"role": "user", "content": enhanced_message})
            
            # Make API request
            headers = {
                "Authorization": f"Bearer {OPENAI_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4",
                "messages": messages,
                "max_tokens": 1500,
                "temperature": 0.7
            }
            
            response = requests.post(OPENAI_API_URL, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                # Add to conversation history
                self.conversation_history.append({"role": "user", "content": user_message})
                self.conversation_history.append({"role": "assistant", "content": ai_response})
                
                return ai_response
            else:
                return f"API Error: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Error getting AI response: {str(e)}"
    
    def extract_symbols_from_message(self, message):
        """Extract stock symbols from user message"""
        import re
        
        # Common stock mappings
        stock_names = {
            'apple': 'AAPL', 'microsoft': 'MSFT', 'tesla': 'TSLA',
            'google': 'GOOGL', 'alphabet': 'GOOGL', 'amazon': 'AMZN', 
            'nvidia': 'NVDA', 'meta': 'META', 'facebook': 'META',
            'netflix': 'NFLX', 'amd': 'AMD', 'intel': 'INTC'
        }
        
        message_lower = message.lower()
        symbols = []
        
        # Check for company names
        for name, symbol in stock_names.items():
            if name in message_lower:
                symbols.append(symbol)
        
        # Look for uppercase symbols
        found_symbols = re.findall(r'\b[A-Z]{1,5}\b', message)
        symbols.extend(found_symbols)
        
        return list(set(symbols))  # Remove duplicates
    
    def get_market_context(self, symbols, user_message):
        """Get comprehensive market context including insider trades, news, and target progress"""
        context = "Real-time Market Data:\n\n"

        # Add current profit target information
        target_info = self.target_manager.get_progress_info()
        if target_info["status"] != "NO_TARGET":
            urgency_emoji = {
                "CRITICAL": "🚨",
                "HIGH": "⚡",
                "MODERATE": "📊",
                "LOW": "😌"
            }.get(target_info["urgency_level"], "📊")

            context += f"""ACTIVE PROFIT TARGET {urgency_emoji}:
- Target: ${target_info['target_amount']:.2f} in {target_info['timeframe_description']}
- Current P&L: ${target_info['current_pnl']:.2f} ({target_info['progress_percentage']:.1f}%)
- Remaining: ${target_info['remaining_amount']:.2f}
- Time Left: {target_info['remaining_time_minutes']:.1f} minutes
- Urgency Level: {target_info['urgency_level']}
- Required Rate: ${target_info['required_profit_rate']:.2f}/minute
- Trades Completed: {target_info['trades_completed']}

"""

        # Check if user is asking about insider trades
        if any(word in user_message.lower() for word in ['insider', 'insider trading', 'insider trades']):
            if symbols:
                # Get insider trades for specific symbols
                for symbol in symbols[:2]:
                    insider_trades = self.market_data.get_insider_trades(symbol)
                    if insider_trades:
                        context += f"Recent Insider Trades for {symbol}:\n"
                        for trade in insider_trades[:3]:
                            context += f"- {trade['insider']} ({trade['title']}): {trade['transaction']} {trade['shares']:,} shares at ${trade['price']:.2f} on {trade['date']}\n"
                        context += "\n"
            else:
                # Get general insider trades
                insider_trades = self.market_data.get_insider_trades()
                if insider_trades:
                    context += "Recent Insider Trades (Popular Stocks):\n"
                    for trade in insider_trades[:5]:
                        context += f"- {trade['symbol']}: {trade['insider']} {trade['transaction']} {trade['shares']:,} shares at ${trade['price']:.2f}\n"
                    context += "\n"

        # Get stock data for mentioned symbols
        if symbols:
            for symbol in symbols[:3]:
                data = self.market_data.get_stock_data(symbol)
                if data:
                    context += f"""
{symbol} ({data['name']}):
- Current Price: ${data['current_price']:.2f}
- Change: {data['change']:+.2f} ({data['change_percent']:+.2f}%)
- Volume: {data['volume']:,}
- Market Cap: ${data['market_cap']/1e9:.1f}B
- P/E Ratio: {data['pe_ratio']:.2f if data['pe_ratio'] else 'N/A'}
- 52W Range: ${data['52w_low']:.2f} - ${data['52w_high']:.2f}
- Sector: {data['sector']}

"""

                    # Get recent news for the symbol
                    news = self.market_data.get_recent_news(symbol)
                    if news:
                        context += f"Recent News for {symbol}:\n"
                        for item in news[:2]:
                            context += f"- {item['title']} ({item['published']})\n"
                        context += "\n"

        return context if len(context) > 50 else None

    def handle_market_analysis_commands(self, message):
        """Handle market analysis and trading plan commands"""
        message_lower = message.lower()

        # Market scan command
        if any(phrase in message_lower for phrase in ['market scan', 'scan market', 'find opportunities', 'trading opportunities']):
            return self.generate_market_scan()

        # Trading plan command
        elif any(phrase in message_lower for phrase in ['trading plan', 'trade plan', 'actionable plan', 'specific trades']):
            return self.generate_comprehensive_trading_plan()

        # Quick opportunities
        elif any(phrase in message_lower for phrase in ['quick trades', 'fast opportunities', 'immediate trades']):
            return self.generate_quick_opportunities()

        return None

    def generate_market_scan(self):
        """Generate comprehensive market scan with actionable opportunities"""
        try:
            progress = self.target_manager.get_progress_info()

            if progress["status"] == "NO_TARGET":
                return """⚠️ **SET A TARGET FIRST**

Please set a profit target first so I can provide targeted recommendations:
• "Set target $500 in 1 day" - For day trading strategies
• "Set target $50 in 30 minutes" - For scalping opportunities
• "Set target $2000 in 1 week" - For swing trading plans

I'll then scan the market and provide specific, actionable trading opportunities!"""

            target_amount = progress["target_amount"]
            urgency_level = progress["urgency_level"]
            timeframe_minutes = progress["remaining_time_minutes"]

            # Generate market scan message
            scan_message = f"""🔍 **REAL-TIME MARKET SCAN** 🔍

🎯 **Your Target**: ${target_amount:.2f} in {progress['timeframe_description']}
⚡ **Urgency Level**: {urgency_level}
⏰ **Time Remaining**: {timeframe_minutes:.1f} minutes

🔄 **Scanning S&P 500 for opportunities...**
📊 **Analyzing technical indicators...**
💹 **Calculating position sizes...**

Please wait while I analyze the market..."""

            # Get opportunities in background
            def get_opportunities():
                opportunities = self.market_analyzer.scan_market_opportunities(
                    target_amount, urgency_level, timeframe_minutes, max_opportunities=5
                )

                if not opportunities:
                    return "❌ **NO HIGH-PROBABILITY OPPORTUNITIES FOUND**\n\nMarket conditions may not be favorable right now. Try again in a few minutes or adjust your target parameters."

                result = f"""✅ **MARKET SCAN COMPLETE** - Found {len(opportunities)} High-Probability Opportunities

{self.get_urgency_trading_advice(urgency_level)}

📊 **TOP TRADING OPPORTUNITIES**:
"""

                for i, opp in enumerate(opportunities, 1):
                    result += f"\n**#{i} OPPORTUNITY**\n{opp.get_formatted_output()}\n"

                result += f"""
💡 **EXECUTION STRATEGY FOR {urgency_level} URGENCY**:
{self.get_execution_strategy(urgency_level)}

⚠️ **RISK MANAGEMENT**:
• Never risk more than 2% of your target on a single trade
• Use stop-losses religiously
• Take profits at targets - don't get greedy
• Monitor positions closely given your {progress['timeframe_description']} timeframe
"""

                return result

            # Return initial message, opportunities will be generated in background
            return scan_message

        except Exception as e:
            return f"❌ Error generating market scan: {str(e)}"

    def generate_comprehensive_trading_plan(self):
        """Generate a comprehensive trading plan with specific actionable steps"""
        try:
            progress = self.target_manager.get_progress_info()

            if progress["status"] == "NO_TARGET":
                return "⚠️ Please set a profit target first using 'Set target $X in Y timeframe'"

            target_amount = progress["target_amount"]
            urgency_level = progress["urgency_level"]
            timeframe_minutes = progress["remaining_time_minutes"]

            # Get top 3 opportunities
            opportunities = self.market_analyzer.scan_market_opportunities(
                target_amount, urgency_level, timeframe_minutes, max_opportunities=3
            )

            if not opportunities:
                return "❌ No suitable opportunities found. Market conditions may not be favorable."

            plan = f"""📋 **COMPREHENSIVE TRADING PLAN** 📋

🎯 **TARGET**: ${target_amount:.2f} in {progress['timeframe_description']}
⚡ **URGENCY**: {urgency_level} ({timeframe_minutes:.1f} minutes remaining)
💰 **CURRENT P&L**: ${progress['current_pnl']:.2f} ({progress['progress_percentage']:.1f}%)
💸 **REMAINING NEEDED**: ${progress['remaining_amount']:.2f}

🚀 **EXECUTION STRATEGY**:
{self.get_detailed_execution_strategy(urgency_level, timeframe_minutes)}

📊 **PRIORITY TRADES** (Execute in order):
"""

            for i, opp in enumerate(opportunities, 1):
                plan += f"""
**TRADE #{i} - {opp.symbol}** ⭐ {opp.probability_score:.0f}% Success Rate
{opp.get_formatted_output()}
🎯 **Action Steps**:
1. Place {opp.execution_method} order at ${opp.entry_price:.2f}
2. Set stop-loss at ${opp.stop_loss:.2f}
3. Set profit target at ${opp.exit_target:.2f}
4. Monitor {opp.timeframe} charts
5. Exit at target or stop - NO EXCEPTIONS

"""

            plan += f"""
⚠️ **RISK MANAGEMENT RULES**:
• Maximum risk per trade: ${target_amount * 0.02:.2f} (2% of target)
• Total portfolio risk: ${target_amount * 0.06:.2f} (6% of target)
• Stop-loss is MANDATORY - no exceptions
• Take profits at targets - don't hold for more

📈 **MONITORING REQUIREMENTS**:
• Check positions every {5 if urgency_level == 'CRITICAL' else 15 if urgency_level == 'HIGH' else 30} minutes
• Watch for volume spikes and news
• Be ready to exit if market conditions change

🎯 **SUCCESS METRICS**:
• Target: {len(opportunities)} trades with avg {sum(opp.profit_potential for opp in opportunities)/len(opportunities):.2f} profit each
• Required win rate: {60 if urgency_level == 'CRITICAL' else 70}% to achieve target
• Maximum acceptable losses: 2 trades before reassessing strategy
"""

            return plan

        except Exception as e:
            return f"❌ Error generating trading plan: {str(e)}"

    def generate_quick_opportunities(self):
        """Generate immediate trading opportunities for urgent situations"""
        try:
            progress = self.target_manager.get_progress_info()

            if progress["status"] == "NO_TARGET":
                return "⚠️ Set a target first: 'Set target $50 in 30 minutes' for quick opportunities"

            # Force critical urgency for quick opportunities
            opportunities = self.market_analyzer.scan_market_opportunities(
                progress["target_amount"], "CRITICAL", 30, max_opportunities=3
            )

            if not opportunities:
                return "❌ No immediate opportunities found. Market may be too volatile or illiquid."

            result = """⚡ **IMMEDIATE TRADING OPPORTUNITIES** ⚡

🚨 **EXECUTE IMMEDIATELY** - These are time-sensitive!

"""

            for i, opp in enumerate(opportunities, 1):
                result += f"""
**QUICK TRADE #{i} - {opp.symbol}**
💰 Current: ${opp.current_price:.2f} → Entry: ${opp.entry_price:.2f} → Target: ${opp.exit_target:.2f}
⚡ **IMMEDIATE ACTION**: Place {opp.execution_method} order NOW
🎯 Profit: ${opp.profit_potential:.2f} | Risk: ${opp.risk_amount:.2f}
📊 Success Rate: {opp.probability_score:.0f}%

"""

            result += """
⚠️ **SPEED EXECUTION RULES**:
• Use MARKET orders for immediate fills
• Set stop-losses IMMEDIATELY after entry
• Take profits at first target - don't wait
• Monitor 1-minute charts only
• Exit within 5-30 minutes maximum
"""

            return result

        except Exception as e:
            return f"❌ Error generating quick opportunities: {str(e)}"

    def get_urgency_trading_advice(self, urgency_level):
        """Get specific trading advice based on urgency level"""
        if urgency_level == "CRITICAL":
            return """🚨 **CRITICAL URGENCY TRADING MODE**:
• Use MARKET orders for immediate execution
• Focus on high-volume, liquid stocks only
• Take ANY profitable trade - don't be picky
• Monitor 1-minute charts exclusively
• Exit positions within 5-15 minutes maximum"""
        elif urgency_level == "HIGH":
            return """⚡ **HIGH URGENCY TRADING MODE**:
• Use LIMIT orders close to market price
• Focus on momentum plays and breakouts
• Target 0.5-1.5% moves quickly
• Monitor 5-minute charts
• Exit positions within 30-60 minutes"""
        elif urgency_level == "MODERATE":
            return """📊 **MODERATE URGENCY TRADING MODE**:
• Use LIMIT orders for better fills
• Focus on technical setups and patterns
• Target 1-3% moves patiently
• Monitor 15-minute charts
• Hold positions for 1-4 hours if needed"""
        else:
            return """😌 **LOW URGENCY TRADING MODE**:
• Use LIMIT orders for optimal entries
• Focus on high-quality setups only
• Target 2-5% moves with patience
• Monitor hourly and daily charts
• Hold positions for days if necessary"""

    def get_execution_strategy(self, urgency_level):
        """Get detailed execution strategy based on urgency"""
        if urgency_level == "CRITICAL":
            return """1. Scan for volume spikes and momentum
2. Enter with MARKET orders immediately
3. Set tight stop-losses (0.2-0.5%)
4. Take profits quickly (0.3-0.8%)
5. Move to next opportunity immediately"""
        elif urgency_level == "HIGH":
            return """1. Look for breakout patterns forming
2. Enter with LIMIT orders near market
3. Set reasonable stop-losses (0.5-1%)
4. Target 1-2% profits
5. Monitor closely for 30-60 minutes"""
        else:
            return """1. Wait for high-probability setups
2. Enter with LIMIT orders at support
3. Set wider stop-losses (1-2%)
4. Target 2-5% profits
5. Hold with patience and discipline"""

    def get_detailed_execution_strategy(self, urgency_level, timeframe_minutes):
        """Get very detailed execution strategy"""
        if timeframe_minutes <= 30:
            return """⚡ **ULTRA-FAST SCALPING STRATEGY**:
• Chart timeframe: 1-minute only
• Entry method: Market orders for speed
• Position size: Larger for quick profits
• Monitoring: Constant attention required
• Exit strategy: Take profits at first sign of reversal"""
        elif timeframe_minutes <= 240:
            return """📈 **AGGRESSIVE DAY TRADING STRATEGY**:
• Chart timeframe: 5-15 minute charts
• Entry method: Limit orders near market price
• Position size: Moderate for balance
• Monitoring: Check every 15-30 minutes
• Exit strategy: Stick to predetermined targets"""
        else:
            return """📊 **PATIENT SWING STRATEGY**:
• Chart timeframe: 1-hour and daily charts
• Entry method: Limit orders at key levels
• Position size: Conservative for safety
• Monitoring: Check 2-3 times per day
• Exit strategy: Hold for full target achievement"""

    def handle_target_commands(self, message):
        """Handle target-related commands"""
        message_lower = message.lower()

        # Set target command
        if any(phrase in message_lower for phrase in ['set target', 'new target', 'profit target']):
            return self.parse_target_from_message(message)

        # Progress check
        elif any(phrase in message_lower for phrase in ['progress', 'target progress', 'how am i doing']):
            return self.get_target_progress_message()

        # Update P&L
        elif any(phrase in message_lower for phrase in ['update pnl', 'add profit', 'trade result']):
            return self.parse_pnl_update(message)

        return None

    def parse_target_from_message(self, message):
        """Parse target setting from natural language"""
        import re

        # Extract amount
        amount_match = re.search(r'\$?(\d+(?:\.\d{2})?)', message)
        if not amount_match:
            return "Please specify a target amount (e.g., '$500' or '100')"

        amount = float(amount_match.group(1))

        # Extract timeframe
        timeframe_patterns = {
            TimeframeUnit.MINUTES: [r'(\d+)\s*min', r'(\d+)\s*minute'],
            TimeframeUnit.HOURS: [r'(\d+)\s*hour', r'(\d+)\s*hr'],
            TimeframeUnit.DAYS: [r'(\d+)\s*day'],
            TimeframeUnit.WEEKS: [r'(\d+)\s*week'],
            TimeframeUnit.MONTHS: [r'(\d+)\s*month']
        }

        timeframe_value = 1
        timeframe_unit = TimeframeUnit.DAYS  # Default

        for unit, patterns in timeframe_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, message.lower())
                if match:
                    timeframe_value = int(match.group(1))
                    timeframe_unit = unit
                    break
            if match:
                break

        # Set the target
        try:
            target = self.target_manager.set_target(amount, timeframe_value, timeframe_unit)
            return f"""🎯 **TARGET SET!**

💰 **Amount**: ${amount:.2f}
⏰ **Timeframe**: {target.get_timeframe_description()}
🏁 **Deadline**: {target.end_time.strftime('%Y-%m-%d %H:%M:%S')}

Your trading strategy will now automatically adapt to this target!
- Ultra-short targets (≤5 min): Ultra-fast scalping
- Short targets (≤30 min): High-frequency trading
- Medium targets (≤4 hours): Day trading
- Long targets (>1 day): Swing trading

Ready to make some profits! 🚀"""

        except Exception as e:
            return f"Error setting target: {str(e)}"

    def get_target_progress_message(self):
        """Get formatted target progress message"""
        progress = self.target_manager.get_progress_info()

        if progress["status"] == "NO_TARGET":
            return """⚠️ **NO ACTIVE TARGET**

You haven't set a profit target yet!

To set a target, just say something like:
• "Set target $500 in 1 day"
• "New target $50 in 2 hours"
• "I want to make $25 in 30 minutes"

I'll automatically adapt my trading advice to your timeframe!"""

        urgency_emoji = {
            "CRITICAL": "🚨",
            "HIGH": "⚡",
            "MODERATE": "📊",
            "LOW": "😌"
        }.get(progress["urgency_level"], "📊")

        status_msg = ""
        if progress["is_achieved"]:
            status_msg = "🎉 **TARGET ACHIEVED!** 🎉"
        elif progress["is_expired"]:
            status_msg = "⏰ **TARGET EXPIRED**"
        else:
            status_msg = f"{urgency_emoji} **{progress['urgency_level']} URGENCY**"

        return f"""{status_msg}

🎯 **TARGET**: ${progress['target_amount']:.2f} in {progress['timeframe_description']}
💰 **CURRENT P&L**: ${progress['current_pnl']:.2f} ({progress['progress_percentage']:.1f}%)
💸 **REMAINING**: ${progress['remaining_amount']:.2f}
⏰ **TIME LEFT**: {progress['remaining_time_minutes']:.1f} minutes
📈 **REQUIRED RATE**: ${progress['required_profit_rate']:.2f}/minute
📊 **TRADES**: {progress['trades_completed']} completed

{self.get_urgency_advice(progress['urgency_level'])}"""

    def get_urgency_advice(self, urgency_level):
        """Get advice based on urgency level"""
        if urgency_level == "CRITICAL":
            return """🚨 **CRITICAL URGENCY ADVICE**:
• Focus on immediate scalping opportunities
• Use market orders for instant execution
• Monitor 1-minute charts only
• Take any profitable trades quickly"""
        elif urgency_level == "HIGH":
            return """⚡ **HIGH URGENCY ADVICE**:
• Accelerate your trading pace
• Focus on high-probability setups
• Use 5-minute charts for faster signals
• Consider slightly larger position sizes"""
        elif urgency_level == "MODERATE":
            return """📊 **MODERATE URGENCY ADVICE**:
• Maintain steady trading pace
• Use proven strategies
• 15-minute charts for good signals
• Balance risk and opportunity"""
        else:
            return """😌 **LOW URGENCY ADVICE**:
• Take your time for quality setups
• Focus on high-probability trades
• Use longer timeframes for analysis
• Prioritize risk management"""

    def parse_pnl_update(self, message):
        """Parse P&L update from message"""
        import re

        # Look for profit/loss amounts
        pnl_match = re.search(r'[\+\-]?\$?(\d+(?:\.\d{2})?)', message)
        if not pnl_match:
            return "Please specify the profit/loss amount (e.g., '+$50' or '-$25')"

        amount = float(pnl_match.group(1))

        # Check if it's a loss
        if '-' in message or any(word in message.lower() for word in ['loss', 'lost', 'down']):
            amount = -amount

        # Update P&L
        self.target_manager.update_pnl(amount)

        progress = self.target_manager.get_progress_info()

        return f"""💰 **P&L UPDATED**

Trade Result: ${amount:+.2f}
New Total: ${progress['current_pnl']:.2f} / ${progress['target_amount']:.2f}
Progress: {progress['progress_percentage']:.1f}%
Remaining: ${progress['remaining_amount']:.2f}

{self.get_target_progress_message()}"""

class ChatGPTTradingGUI:
    """ChatGPT-style interface for trading bot"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 ChatGPT Trading Bot - FLEXIBLE PROFIT TARGETING")
        self.root.geometry("1000x700")
        self.root.configure(bg='#343541')
        
        self.bot = ChatGPTTradingBot()
        self.create_widgets()
        self.setup_layout()
        
        # Welcome message with flexible targeting and market analysis
        self.add_message("🤖 AI Trading Assistant",
                        "Hello! I'm your AI trading assistant powered by GPT-4 with FLEXIBLE PROFIT TARGETING and REAL-TIME MARKET ANALYSIS!\n\n" +
                        "🎯 **FLEXIBLE PROFIT TARGETS**: Set ANY amount with ANY timeframe!\n" +
                        "   • '$50 in 30 minutes' - Ultra-fast scalping\n" +
                        "   • '$500 in 1 day' - Day trading strategies\n" +
                        "   • '$2000 in 1 week' - Swing trading approach\n\n" +
                        "📊 **REAL-TIME MARKET ANALYSIS**: Get actionable trading opportunities!\n" +
                        "   • Scans S&P 500 stocks with technical indicators\n" +
                        "   • Provides specific entry/exit prices and position sizes\n" +
                        "   • Adapts strategies based on your target urgency\n" +
                        "   • Calculates risk/reward ratios and success probabilities\n\n" +
                        "🚀 **TARGETING COMMANDS**:\n" +
                        "   • 'Set target $500 in 1 day' - Set your profit goal\n" +
                        "   • 'Progress' - Check current target status\n" +
                        "   • 'Update PnL +$50' - Record trade results\n\n" +
                        "📈 **MARKET ANALYSIS COMMANDS**:\n" +
                        "   • 'Market scan' - Find top trading opportunities\n" +
                        "   • 'Trading plan' - Get comprehensive actionable plan\n" +
                        "   • 'Quick opportunities' - Immediate trades for urgent targets\n\n" +
                        "💡 **GET STARTED**: Set a target first, then ask for market analysis!\n" +
                        "Example: 'Set target $200 in 4 hours' then 'Market scan'")
    
    def create_widgets(self):
        """Create ChatGPT-style interface"""
        
        # Main chat area
        self.chat_frame = tk.Frame(self.root, bg='#343541')
        
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame,
            bg='#444654',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            state=tk.DISABLED,
            insertbackground='#ffffff'
        )
        
        # Input area
        self.input_frame = tk.Frame(self.root, bg='#40414f')
        
        # Input text area
        self.input_text = tk.Text(
            self.input_frame,
            height=3,
            bg='#40414f',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            insertbackground='#ffffff',
            relief=tk.FLAT,
            bd=10
        )
        
        # Send button
        self.send_button = tk.Button(
            self.input_frame,
            text="Send",
            command=self.send_message,
            bg='#10a37f',
            fg='#ffffff',
            font=('Segoe UI', 10, 'bold'),
            relief=tk.FLAT,
            padx=20,
            cursor='hand2'
        )
        
        # Status bar for target progress
        self.status_frame = tk.Frame(self.root, bg='#2d2d30', height=30)
        self.status_label = tk.Label(
            self.status_frame,
            text="🎯 No active target - Set a profit target to get started!",
            bg='#2d2d30',
            fg='#ffffff',
            font=('Segoe UI', 9),
            anchor='w'
        )

        # Bind Enter key (Shift+Enter for new line)
        self.input_text.bind('<Return>', self.on_enter)
        self.input_text.bind('<Shift-Return>', self.on_shift_enter)

        # Start status update timer
        self.update_status()
        self.root.after(5000, self.update_status_timer)  # Update every 5 seconds
        
        # API key setup button
        self.setup_button = tk.Button(
            self.input_frame,
            text="Setup API Key",
            command=self.setup_api_key,
            bg='#ff6b6b',
            fg='#ffffff',
            font=('Segoe UI', 9),
            relief=tk.FLAT
        )
    
    def setup_layout(self):
        """Setup ChatGPT-style layout with status bar"""
        # Status bar at top
        self.status_frame.pack(fill=tk.X, side=tk.TOP)
        self.status_label.pack(fill=tk.X, padx=10, pady=5)

        # Chat area
        self.chat_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 0))
        self.chat_display.pack(fill=tk.BOTH, expand=True)

        # Input area
        self.input_frame.pack(fill=tk.X, padx=10, pady=10)

        # Input layout
        self.setup_button.pack(side=tk.RIGHT, padx=(10, 0))
        self.send_button.pack(side=tk.RIGHT, padx=(10, 0))
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    def add_message(self, sender, message):
        """Add message to chat display"""
        self.chat_display.config(state=tk.NORMAL)
        
        # Add timestamp
        timestamp = datetime.now().strftime("%H:%M")
        
        # Add sender and message
        self.chat_display.insert(tk.END, f"\n{sender} [{timestamp}]\n", "sender")
        self.chat_display.insert(tk.END, f"{message}\n\n", "message")
        
        # Configure tags for styling
        self.chat_display.tag_config("sender", foreground="#10a37f", font=('Segoe UI', 11, 'bold'))
        self.chat_display.tag_config("message", foreground="#ffffff", font=('Segoe UI', 11))
        
        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)
    
    def send_message(self):
        """Send message to AI with flexible targeting support"""
        message = self.input_text.get("1.0", tk.END).strip()
        if not message:
            return

        # Clear input
        self.input_text.delete("1.0", tk.END)

        # Add user message
        self.add_message("👤 You", message)

        # Check for target-related commands first
        target_response = self.bot.handle_target_commands(message)
        if target_response:
            self.add_message("🎯 Target Manager", target_response)
            return

        # Check for market analysis commands
        market_response = self.bot.handle_market_analysis_commands(message)
        if market_response:
            self.add_message("📊 Market Analyzer", market_response)
            return

        # Show thinking indicator
        self.add_message("🤖 AI Assistant", "🤔 Thinking...")

        # Process in background
        def get_response():
            try:
                # Extract symbols and get market context (now includes target info)
                symbols = self.bot.extract_symbols_from_message(message)
                market_context = self.bot.get_market_context(symbols, message)

                # Get AI response
                response = self.bot.get_ai_response(message, market_context)

                # Remove thinking message and add real response
                self.chat_display.config(state=tk.NORMAL)

                # Find and remove the last "Thinking..." message
                content = self.chat_display.get("1.0", tk.END)
                lines = content.split('\n')

                # Remove last few lines (thinking message)
                for _ in range(4):
                    if lines:
                        lines.pop()

                # Rebuild content without thinking message
                new_content = '\n'.join(lines)
                self.chat_display.delete("1.0", tk.END)
                self.chat_display.insert("1.0", new_content)

                self.chat_display.config(state=tk.DISABLED)

                # Add real response
                self.add_message("🤖 AI Assistant", response)

            except Exception as e:
                self.add_message("❌ Error", f"Failed to get response: {str(e)}")

        threading.Thread(target=get_response, daemon=True).start()
    
    def on_enter(self, event):
        """Handle Enter key"""
        self.send_message()
        return "break"
    
    def on_shift_enter(self, event):
        """Handle Shift+Enter for new line"""
        return None

    def update_status(self):
        """Update the status bar with current target progress"""
        progress = self.bot.target_manager.get_progress_info()

        if progress["status"] == "NO_TARGET":
            self.status_label.config(
                text="🎯 No active target - Say 'Set target $500 in 1 day' to get started!",
                fg='#ffffff'
            )
        else:
            urgency_colors = {
                "CRITICAL": "#ff4444",
                "HIGH": "#ff8800",
                "MODERATE": "#ffaa00",
                "LOW": "#00aa00"
            }

            urgency_emojis = {
                "CRITICAL": "🚨",
                "HIGH": "⚡",
                "MODERATE": "📊",
                "LOW": "😌"
            }

            urgency = progress["urgency_level"]
            emoji = urgency_emojis.get(urgency, "📊")
            color = urgency_colors.get(urgency, "#ffffff")

            status_text = f"{emoji} ${progress['current_pnl']:.2f}/${progress['target_amount']:.2f} ({progress['progress_percentage']:.1f}%) | {progress['timeframe_description']} | {progress['remaining_time_minutes']:.1f}m left | {urgency} URGENCY"

            self.status_label.config(text=status_text, fg=color)

    def update_status_timer(self):
        """Timer function to update status periodically"""
        self.update_status()
        self.root.after(5000, self.update_status_timer)  # Update every 5 seconds

    def setup_api_key(self):
        """Setup OpenAI API key"""
        api_key = simpledialog.askstring(
            "OpenAI API Key",
            "Enter your OpenAI API key:",
            show='*'
        )
        
        if api_key:
            global OPENAI_API_KEY
            OPENAI_API_KEY = api_key
            self.add_message("✅ System", "API key configured! You can now chat with the AI.")
        else:
            self.add_message("⚠️ System", "API key required for AI responses.")
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Launch ChatGPT-style trading bot with flexible targeting"""

    print("🚀 Launching ChatGPT Trading Bot with FLEXIBLE PROFIT TARGETING...")
    print("🎯 Set ANY profit target with ANY timeframe!")
    print("📊 Real AI conversations about trading and markets")
    print("💰 Examples: 'Set target $50 in 30 minutes' or 'New target $500 in 1 day'")

    app = ChatGPTTradingGUI()
    app.run()

if __name__ == "__main__":
    main()
