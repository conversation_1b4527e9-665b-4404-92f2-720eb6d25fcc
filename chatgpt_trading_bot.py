#!/usr/bin/env python3
"""
ChatGPT-Style Trading Bot
Real OpenAI API integration for intelligent trading conversations
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox, simpledialog, ttk
import threading
import time
from datetime import datetime, timedelta
import yfinance as yf
import requests
import json
import os
from typing import Optional
from dataclasses import dataclass
from enum import Enum

# OpenAI API Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

# Flexible Profit Targeting System
class TimeframeUnit(Enum):
    MINUTES = "minutes"
    HOURS = "hours"
    DAYS = "days"
    WEEKS = "weeks"
    MONTHS = "months"

@dataclass
class ProfitTarget:
    amount: float
    timeframe_value: int
    timeframe_unit: TimeframeUnit
    start_time: datetime
    end_time: datetime
    current_pnl: float = 0.0

    @property
    def remaining_amount(self) -> float:
        return max(0, self.amount - self.current_pnl)

    @property
    def remaining_time_seconds(self) -> float:
        return max(0, (self.end_time - datetime.now()).total_seconds())

    @property
    def remaining_time_minutes(self) -> float:
        return self.remaining_time_seconds / 60

    @property
    def progress_percentage(self) -> float:
        return min(100, (self.current_pnl / self.amount) * 100) if self.amount > 0 else 0

    @property
    def time_elapsed_percentage(self) -> float:
        total_seconds = (self.end_time - self.start_time).total_seconds()
        elapsed_seconds = (datetime.now() - self.start_time).total_seconds()
        return min(100, (elapsed_seconds / total_seconds) * 100) if total_seconds > 0 else 0

    @property
    def urgency_level(self) -> str:
        time_remaining_pct = 100 - self.time_elapsed_percentage
        progress_pct = self.progress_percentage

        if time_remaining_pct < 10 and progress_pct < 50:
            return "CRITICAL"
        elif time_remaining_pct < 25 and progress_pct < 75:
            return "HIGH"
        elif time_remaining_pct < 50:
            return "MODERATE"
        else:
            return "LOW"

    @property
    def is_expired(self) -> bool:
        return datetime.now() >= self.end_time

    @property
    def is_achieved(self) -> bool:
        return self.current_pnl >= self.amount

    def get_required_profit_rate(self) -> float:
        """Get required profit per minute to achieve target"""
        remaining_minutes = self.remaining_time_minutes
        if remaining_minutes <= 0:
            return float('inf')
        return self.remaining_amount / remaining_minutes

    def get_timeframe_description(self) -> str:
        """Get human-readable timeframe description"""
        if self.timeframe_value == 1:
            unit_name = self.timeframe_unit.value[:-1]  # Remove 's'
        else:
            unit_name = self.timeframe_unit.value

        return f"{self.timeframe_value} {unit_name}"

    def get_status_summary(self) -> str:
        """Get comprehensive status summary"""
        return f"${self.current_pnl:.2f}/${self.amount:.2f} ({self.progress_percentage:.1f}%) in {self.get_timeframe_description()}"

class FlexibleTargetManager:
    """Manages flexible profit targets"""

    def __init__(self):
        self.current_target: Optional[ProfitTarget] = None
        self.trades_completed = 0

    def set_target(self, amount: float, timeframe_value: int, timeframe_unit: TimeframeUnit) -> ProfitTarget:
        """Set a new profit target"""
        start_time = datetime.now()

        # Calculate end time based on timeframe
        if timeframe_unit == TimeframeUnit.MINUTES:
            end_time = start_time + timedelta(minutes=timeframe_value)
        elif timeframe_unit == TimeframeUnit.HOURS:
            end_time = start_time + timedelta(hours=timeframe_value)
        elif timeframe_unit == TimeframeUnit.DAYS:
            end_time = start_time + timedelta(days=timeframe_value)
        elif timeframe_unit == TimeframeUnit.WEEKS:
            end_time = start_time + timedelta(weeks=timeframe_value)
        elif timeframe_unit == TimeframeUnit.MONTHS:
            end_time = start_time + timedelta(days=timeframe_value * 30)  # Approximate
        else:
            raise ValueError(f"Unsupported timeframe unit: {timeframe_unit}")

        self.current_target = ProfitTarget(
            amount=amount,
            timeframe_value=timeframe_value,
            timeframe_unit=timeframe_unit,
            start_time=start_time,
            end_time=end_time
        )

        self.trades_completed = 0
        return self.current_target

    def update_pnl(self, trade_pnl: float):
        """Update P&L for current target"""
        if self.current_target:
            self.current_target.current_pnl += trade_pnl
            self.trades_completed += 1

    def get_progress_info(self) -> dict:
        """Get current progress information"""
        if not self.current_target:
            return {"status": "NO_TARGET", "message": "No active profit target set"}

        target = self.current_target
        return {
            "target_amount": target.amount,
            "current_pnl": target.current_pnl,
            "remaining_amount": target.remaining_amount,
            "progress_percentage": target.progress_percentage,
            "timeframe_description": target.get_timeframe_description(),
            "time_elapsed_percentage": target.time_elapsed_percentage,
            "remaining_time_minutes": target.remaining_time_minutes,
            "urgency_level": target.urgency_level,
            "required_profit_rate": target.get_required_profit_rate(),
            "trades_completed": self.trades_completed,
            "status_summary": target.get_status_summary(),
            "is_achieved": target.is_achieved,
            "is_expired": target.is_expired,
            "status": "ACTIVE"
        }

class RealTimeMarketData:
    """Get real market data for AI context"""

    def get_stock_data(self, symbol):
        """Get current stock data"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="5d")

            if hist.empty:
                return None

            current_price = info.get('currentPrice') or hist['Close'].iloc[-1]
            previous_close = info.get('previousClose') or hist['Close'].iloc[-2]
            change = current_price - previous_close
            change_percent = (change / previous_close) * 100

            return {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'current_price': current_price,
                'change': change,
                'change_percent': change_percent,
                'volume': hist['Volume'].iloc[-1],
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'sector': info.get('sector'),
                '52w_high': info.get('fiftyTwoWeekHigh'),
                '52w_low': info.get('fiftyTwoWeekLow')
            }
        except Exception as e:
            print(f"Error getting data for {symbol}: {e}")
            return None

    def get_insider_trades(self, symbol=None):
        """Get recent insider trading data"""
        try:
            if symbol:
                # Get insider trades for specific symbol
                ticker = yf.Ticker(symbol)
                insider_trades = ticker.insider_transactions

                if insider_trades is not None and not insider_trades.empty:
                    # Get recent trades (last 10)
                    recent_trades = insider_trades.head(10)

                    trades_data = []
                    for _, trade in recent_trades.iterrows():
                        trades_data.append({
                            'symbol': symbol,
                            'insider': trade.get('Insider', 'Unknown'),
                            'title': trade.get('Title', 'Unknown'),
                            'transaction': trade.get('Transaction', 'Unknown'),
                            'shares': trade.get('Shares', 0),
                            'price': trade.get('Price', 0),
                            'date': trade.get('Date', 'Unknown')
                        })

                    return trades_data
            else:
                # Get insider trades for popular stocks
                popular_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
                all_trades = []

                for stock in popular_stocks:
                    trades = self.get_insider_trades(stock)
                    if trades:
                        all_trades.extend(trades[:2])  # Get top 2 from each

                return all_trades

        except Exception as e:
            print(f"Error getting insider trades: {e}")
            return []

    def get_recent_news(self, symbol=None):
        """Get recent market news"""
        try:
            if symbol:
                ticker = yf.Ticker(symbol)
                news = ticker.news

                if news:
                    return [{
                        'title': item.get('title', ''),
                        'publisher': item.get('publisher', ''),
                        'link': item.get('link', ''),
                        'published': datetime.fromtimestamp(item.get('providerPublishTime', 0)).strftime('%Y-%m-%d %H:%M')
                    } for item in news[:5]]

            return []
        except Exception as e:
            print(f"Error getting news: {e}")
            return []
    
    def get_market_overview(self):
        """Get market indices overview"""
        indices = {
            '^GSPC': 'S&P 500',
            '^IXIC': 'NASDAQ',
            '^DJI': 'Dow Jones'
        }
        
        market_data = {}
        for symbol, name in indices.items():
            data = self.get_stock_data(symbol)
            if data:
                market_data[name] = data
        
        return market_data

class ChatGPTTradingBot:
    """ChatGPT-style trading bot with OpenAI API and flexible profit targeting"""

    def __init__(self):
        self.market_data = RealTimeMarketData()
        self.conversation_history = []
        self.target_manager = FlexibleTargetManager()

        # System prompt for trading expertise with flexible targeting
        self.system_prompt = """You are a professional trading and investment advisor with deep expertise in:

- Technical analysis (moving averages, RSI, MACD, support/resistance)
- Fundamental analysis (P/E ratios, earnings, financial statements)
- Market trends and sector analysis
- Risk management and position sizing
- Options trading strategies
- Cryptocurrency analysis
- Economic indicators and their market impact
- Insider trading analysis and interpretation
- FLEXIBLE PROFIT TARGETING: Helping users set and achieve profit targets with any amount and any timeframe

You have access to REAL-TIME market data including:
- Current stock prices, volume, and technical indicators
- Recent insider trading transactions
- Latest market news and developments
- Sector performance data
- FLEXIBLE PROFIT TARGET TRACKING: Current target progress, urgency levels, and time remaining

FLEXIBLE TARGETING CAPABILITIES:
You can help users set profit targets with ANY amount ($5 to $50,000+) and ANY timeframe (5 minutes to months). You automatically adapt your trading advice based on:
- Target amount and timeframe
- Current progress toward target
- Time remaining (urgency level: CRITICAL, HIGH, MODERATE, LOW)
- Required profit rate per minute

When users have active targets, you provide urgency-appropriate advice:
- CRITICAL urgency (< 10% time left): Ultra-fast scalping, immediate execution
- HIGH urgency (< 25% time left): Aggressive day trading, quick opportunities
- MODERATE urgency (< 50% time left): Balanced approach
- LOW urgency (> 50% time left): Patient, high-quality setups

You provide clear, actionable trading advice while always emphasizing risk management. When users ask about insider trades, market data, or specific stocks, you ALWAYS pull and analyze the current real-time data. Never say you don't have access to data - you do have access and should use it.

You are knowledgeable, professional, and direct. You don't just give generic advice - you provide specific, actionable insights based on current market conditions, real data, and their active profit targets."""

    def get_ai_response(self, user_message, market_context=None):
        """Get response from OpenAI API"""
        try:
            # Prepare the message with market context
            if market_context:
                enhanced_message = f"""
User Question: {user_message}

Current Market Data:
{market_context}

Please provide a comprehensive analysis considering this real-time data.
"""
            else:
                enhanced_message = user_message
            
            # Prepare conversation for API
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (last 10 messages)
            for msg in self.conversation_history[-10:]:
                messages.append(msg)
            
            # Add current message
            messages.append({"role": "user", "content": enhanced_message})
            
            # Make API request
            headers = {
                "Authorization": f"Bearer {OPENAI_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4",
                "messages": messages,
                "max_tokens": 1500,
                "temperature": 0.7
            }
            
            response = requests.post(OPENAI_API_URL, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                # Add to conversation history
                self.conversation_history.append({"role": "user", "content": user_message})
                self.conversation_history.append({"role": "assistant", "content": ai_response})
                
                return ai_response
            else:
                return f"API Error: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Error getting AI response: {str(e)}"
    
    def extract_symbols_from_message(self, message):
        """Extract stock symbols from user message"""
        import re
        
        # Common stock mappings
        stock_names = {
            'apple': 'AAPL', 'microsoft': 'MSFT', 'tesla': 'TSLA',
            'google': 'GOOGL', 'alphabet': 'GOOGL', 'amazon': 'AMZN', 
            'nvidia': 'NVDA', 'meta': 'META', 'facebook': 'META',
            'netflix': 'NFLX', 'amd': 'AMD', 'intel': 'INTC'
        }
        
        message_lower = message.lower()
        symbols = []
        
        # Check for company names
        for name, symbol in stock_names.items():
            if name in message_lower:
                symbols.append(symbol)
        
        # Look for uppercase symbols
        found_symbols = re.findall(r'\b[A-Z]{1,5}\b', message)
        symbols.extend(found_symbols)
        
        return list(set(symbols))  # Remove duplicates
    
    def get_market_context(self, symbols, user_message):
        """Get comprehensive market context including insider trades, news, and target progress"""
        context = "Real-time Market Data:\n\n"

        # Add current profit target information
        target_info = self.target_manager.get_progress_info()
        if target_info["status"] != "NO_TARGET":
            urgency_emoji = {
                "CRITICAL": "🚨",
                "HIGH": "⚡",
                "MODERATE": "📊",
                "LOW": "😌"
            }.get(target_info["urgency_level"], "📊")

            context += f"""ACTIVE PROFIT TARGET {urgency_emoji}:
- Target: ${target_info['target_amount']:.2f} in {target_info['timeframe_description']}
- Current P&L: ${target_info['current_pnl']:.2f} ({target_info['progress_percentage']:.1f}%)
- Remaining: ${target_info['remaining_amount']:.2f}
- Time Left: {target_info['remaining_time_minutes']:.1f} minutes
- Urgency Level: {target_info['urgency_level']}
- Required Rate: ${target_info['required_profit_rate']:.2f}/minute
- Trades Completed: {target_info['trades_completed']}

"""

        # Check if user is asking about insider trades
        if any(word in user_message.lower() for word in ['insider', 'insider trading', 'insider trades']):
            if symbols:
                # Get insider trades for specific symbols
                for symbol in symbols[:2]:
                    insider_trades = self.market_data.get_insider_trades(symbol)
                    if insider_trades:
                        context += f"Recent Insider Trades for {symbol}:\n"
                        for trade in insider_trades[:3]:
                            context += f"- {trade['insider']} ({trade['title']}): {trade['transaction']} {trade['shares']:,} shares at ${trade['price']:.2f} on {trade['date']}\n"
                        context += "\n"
            else:
                # Get general insider trades
                insider_trades = self.market_data.get_insider_trades()
                if insider_trades:
                    context += "Recent Insider Trades (Popular Stocks):\n"
                    for trade in insider_trades[:5]:
                        context += f"- {trade['symbol']}: {trade['insider']} {trade['transaction']} {trade['shares']:,} shares at ${trade['price']:.2f}\n"
                    context += "\n"

        # Get stock data for mentioned symbols
        if symbols:
            for symbol in symbols[:3]:
                data = self.market_data.get_stock_data(symbol)
                if data:
                    context += f"""
{symbol} ({data['name']}):
- Current Price: ${data['current_price']:.2f}
- Change: {data['change']:+.2f} ({data['change_percent']:+.2f}%)
- Volume: {data['volume']:,}
- Market Cap: ${data['market_cap']/1e9:.1f}B
- P/E Ratio: {data['pe_ratio']:.2f if data['pe_ratio'] else 'N/A'}
- 52W Range: ${data['52w_low']:.2f} - ${data['52w_high']:.2f}
- Sector: {data['sector']}

"""

                    # Get recent news for the symbol
                    news = self.market_data.get_recent_news(symbol)
                    if news:
                        context += f"Recent News for {symbol}:\n"
                        for item in news[:2]:
                            context += f"- {item['title']} ({item['published']})\n"
                        context += "\n"

        return context if len(context) > 50 else None

    def handle_target_commands(self, message):
        """Handle target-related commands"""
        message_lower = message.lower()

        # Set target command
        if any(phrase in message_lower for phrase in ['set target', 'new target', 'profit target']):
            return self.parse_target_from_message(message)

        # Progress check
        elif any(phrase in message_lower for phrase in ['progress', 'target progress', 'how am i doing']):
            return self.get_target_progress_message()

        # Update P&L
        elif any(phrase in message_lower for phrase in ['update pnl', 'add profit', 'trade result']):
            return self.parse_pnl_update(message)

        return None

    def parse_target_from_message(self, message):
        """Parse target setting from natural language"""
        import re

        # Extract amount
        amount_match = re.search(r'\$?(\d+(?:\.\d{2})?)', message)
        if not amount_match:
            return "Please specify a target amount (e.g., '$500' or '100')"

        amount = float(amount_match.group(1))

        # Extract timeframe
        timeframe_patterns = {
            TimeframeUnit.MINUTES: [r'(\d+)\s*min', r'(\d+)\s*minute'],
            TimeframeUnit.HOURS: [r'(\d+)\s*hour', r'(\d+)\s*hr'],
            TimeframeUnit.DAYS: [r'(\d+)\s*day'],
            TimeframeUnit.WEEKS: [r'(\d+)\s*week'],
            TimeframeUnit.MONTHS: [r'(\d+)\s*month']
        }

        timeframe_value = 1
        timeframe_unit = TimeframeUnit.DAYS  # Default

        for unit, patterns in timeframe_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, message.lower())
                if match:
                    timeframe_value = int(match.group(1))
                    timeframe_unit = unit
                    break
            if match:
                break

        # Set the target
        try:
            target = self.target_manager.set_target(amount, timeframe_value, timeframe_unit)
            return f"""🎯 **TARGET SET!**

💰 **Amount**: ${amount:.2f}
⏰ **Timeframe**: {target.get_timeframe_description()}
🏁 **Deadline**: {target.end_time.strftime('%Y-%m-%d %H:%M:%S')}

Your trading strategy will now automatically adapt to this target!
- Ultra-short targets (≤5 min): Ultra-fast scalping
- Short targets (≤30 min): High-frequency trading
- Medium targets (≤4 hours): Day trading
- Long targets (>1 day): Swing trading

Ready to make some profits! 🚀"""

        except Exception as e:
            return f"Error setting target: {str(e)}"

    def get_target_progress_message(self):
        """Get formatted target progress message"""
        progress = self.target_manager.get_progress_info()

        if progress["status"] == "NO_TARGET":
            return """⚠️ **NO ACTIVE TARGET**

You haven't set a profit target yet!

To set a target, just say something like:
• "Set target $500 in 1 day"
• "New target $50 in 2 hours"
• "I want to make $25 in 30 minutes"

I'll automatically adapt my trading advice to your timeframe!"""

        urgency_emoji = {
            "CRITICAL": "🚨",
            "HIGH": "⚡",
            "MODERATE": "📊",
            "LOW": "😌"
        }.get(progress["urgency_level"], "📊")

        status_msg = ""
        if progress["is_achieved"]:
            status_msg = "🎉 **TARGET ACHIEVED!** 🎉"
        elif progress["is_expired"]:
            status_msg = "⏰ **TARGET EXPIRED**"
        else:
            status_msg = f"{urgency_emoji} **{progress['urgency_level']} URGENCY**"

        return f"""{status_msg}

🎯 **TARGET**: ${progress['target_amount']:.2f} in {progress['timeframe_description']}
💰 **CURRENT P&L**: ${progress['current_pnl']:.2f} ({progress['progress_percentage']:.1f}%)
💸 **REMAINING**: ${progress['remaining_amount']:.2f}
⏰ **TIME LEFT**: {progress['remaining_time_minutes']:.1f} minutes
📈 **REQUIRED RATE**: ${progress['required_profit_rate']:.2f}/minute
📊 **TRADES**: {progress['trades_completed']} completed

{self.get_urgency_advice(progress['urgency_level'])}"""

    def get_urgency_advice(self, urgency_level):
        """Get advice based on urgency level"""
        if urgency_level == "CRITICAL":
            return """🚨 **CRITICAL URGENCY ADVICE**:
• Focus on immediate scalping opportunities
• Use market orders for instant execution
• Monitor 1-minute charts only
• Take any profitable trades quickly"""
        elif urgency_level == "HIGH":
            return """⚡ **HIGH URGENCY ADVICE**:
• Accelerate your trading pace
• Focus on high-probability setups
• Use 5-minute charts for faster signals
• Consider slightly larger position sizes"""
        elif urgency_level == "MODERATE":
            return """📊 **MODERATE URGENCY ADVICE**:
• Maintain steady trading pace
• Use proven strategies
• 15-minute charts for good signals
• Balance risk and opportunity"""
        else:
            return """😌 **LOW URGENCY ADVICE**:
• Take your time for quality setups
• Focus on high-probability trades
• Use longer timeframes for analysis
• Prioritize risk management"""

    def parse_pnl_update(self, message):
        """Parse P&L update from message"""
        import re

        # Look for profit/loss amounts
        pnl_match = re.search(r'[\+\-]?\$?(\d+(?:\.\d{2})?)', message)
        if not pnl_match:
            return "Please specify the profit/loss amount (e.g., '+$50' or '-$25')"

        amount = float(pnl_match.group(1))

        # Check if it's a loss
        if '-' in message or any(word in message.lower() for word in ['loss', 'lost', 'down']):
            amount = -amount

        # Update P&L
        self.target_manager.update_pnl(amount)

        progress = self.target_manager.get_progress_info()

        return f"""💰 **P&L UPDATED**

Trade Result: ${amount:+.2f}
New Total: ${progress['current_pnl']:.2f} / ${progress['target_amount']:.2f}
Progress: {progress['progress_percentage']:.1f}%
Remaining: ${progress['remaining_amount']:.2f}

{self.get_target_progress_message()}"""

class ChatGPTTradingGUI:
    """ChatGPT-style interface for trading bot"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 ChatGPT Trading Bot - FLEXIBLE PROFIT TARGETING")
        self.root.geometry("1000x700")
        self.root.configure(bg='#343541')
        
        self.bot = ChatGPTTradingBot()
        self.create_widgets()
        self.setup_layout()
        
        # Welcome message with flexible targeting
        self.add_message("🤖 AI Trading Assistant",
                        "Hello! I'm your AI trading assistant powered by GPT-4 with FLEXIBLE PROFIT TARGETING! I can help you with:\n\n" +
                        "🎯 **FLEXIBLE PROFIT TARGETS**: Set ANY amount with ANY timeframe!\n" +
                        "   • '$50 in 30 minutes' - High-frequency trading\n" +
                        "   • '$500 in 1 day' - Day trading strategies\n" +
                        "   • '$2000 in 1 week' - Swing trading approach\n\n" +
                        "📊 Stock analysis and recommendations\n" +
                        "📈 Technical analysis and chart patterns\n" +
                        "💰 Investment strategies and risk management\n" +
                        "🔍 Market research and sector analysis\n" +
                        "📉 Options trading strategies\n\n" +
                        "🚀 **FLEXIBLE TARGETING COMMANDS**:\n" +
                        "   • 'Set target $500 in 1 day'\n" +
                        "   • 'New target $25 in 2 hours'\n" +
                        "   • 'Progress' - Check your current target\n" +
                        "   • 'Update PnL +$50' - Record trade results\n\n" +
                        "Just ask me anything about trading or set your profit target to get started!")
    
    def create_widgets(self):
        """Create ChatGPT-style interface"""
        
        # Main chat area
        self.chat_frame = tk.Frame(self.root, bg='#343541')
        
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame,
            bg='#444654',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            state=tk.DISABLED,
            insertbackground='#ffffff'
        )
        
        # Input area
        self.input_frame = tk.Frame(self.root, bg='#40414f')
        
        # Input text area
        self.input_text = tk.Text(
            self.input_frame,
            height=3,
            bg='#40414f',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            insertbackground='#ffffff',
            relief=tk.FLAT,
            bd=10
        )
        
        # Send button
        self.send_button = tk.Button(
            self.input_frame,
            text="Send",
            command=self.send_message,
            bg='#10a37f',
            fg='#ffffff',
            font=('Segoe UI', 10, 'bold'),
            relief=tk.FLAT,
            padx=20,
            cursor='hand2'
        )
        
        # Status bar for target progress
        self.status_frame = tk.Frame(self.root, bg='#2d2d30', height=30)
        self.status_label = tk.Label(
            self.status_frame,
            text="🎯 No active target - Set a profit target to get started!",
            bg='#2d2d30',
            fg='#ffffff',
            font=('Segoe UI', 9),
            anchor='w'
        )

        # Bind Enter key (Shift+Enter for new line)
        self.input_text.bind('<Return>', self.on_enter)
        self.input_text.bind('<Shift-Return>', self.on_shift_enter)

        # Start status update timer
        self.update_status()
        self.root.after(5000, self.update_status_timer)  # Update every 5 seconds
        
        # API key setup button
        self.setup_button = tk.Button(
            self.input_frame,
            text="Setup API Key",
            command=self.setup_api_key,
            bg='#ff6b6b',
            fg='#ffffff',
            font=('Segoe UI', 9),
            relief=tk.FLAT
        )
    
    def setup_layout(self):
        """Setup ChatGPT-style layout with status bar"""
        # Status bar at top
        self.status_frame.pack(fill=tk.X, side=tk.TOP)
        self.status_label.pack(fill=tk.X, padx=10, pady=5)

        # Chat area
        self.chat_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 0))
        self.chat_display.pack(fill=tk.BOTH, expand=True)

        # Input area
        self.input_frame.pack(fill=tk.X, padx=10, pady=10)

        # Input layout
        self.setup_button.pack(side=tk.RIGHT, padx=(10, 0))
        self.send_button.pack(side=tk.RIGHT, padx=(10, 0))
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    def add_message(self, sender, message):
        """Add message to chat display"""
        self.chat_display.config(state=tk.NORMAL)
        
        # Add timestamp
        timestamp = datetime.now().strftime("%H:%M")
        
        # Add sender and message
        self.chat_display.insert(tk.END, f"\n{sender} [{timestamp}]\n", "sender")
        self.chat_display.insert(tk.END, f"{message}\n\n", "message")
        
        # Configure tags for styling
        self.chat_display.tag_config("sender", foreground="#10a37f", font=('Segoe UI', 11, 'bold'))
        self.chat_display.tag_config("message", foreground="#ffffff", font=('Segoe UI', 11))
        
        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)
    
    def send_message(self):
        """Send message to AI with flexible targeting support"""
        message = self.input_text.get("1.0", tk.END).strip()
        if not message:
            return

        # Clear input
        self.input_text.delete("1.0", tk.END)

        # Add user message
        self.add_message("👤 You", message)

        # Check for target-related commands first
        target_response = self.bot.handle_target_commands(message)
        if target_response:
            self.add_message("🎯 Target Manager", target_response)
            return

        # Show thinking indicator
        self.add_message("🤖 AI Assistant", "🤔 Thinking...")

        # Process in background
        def get_response():
            try:
                # Extract symbols and get market context (now includes target info)
                symbols = self.bot.extract_symbols_from_message(message)
                market_context = self.bot.get_market_context(symbols, message)

                # Get AI response
                response = self.bot.get_ai_response(message, market_context)

                # Remove thinking message and add real response
                self.chat_display.config(state=tk.NORMAL)

                # Find and remove the last "Thinking..." message
                content = self.chat_display.get("1.0", tk.END)
                lines = content.split('\n')

                # Remove last few lines (thinking message)
                for _ in range(4):
                    if lines:
                        lines.pop()

                # Rebuild content without thinking message
                new_content = '\n'.join(lines)
                self.chat_display.delete("1.0", tk.END)
                self.chat_display.insert("1.0", new_content)

                self.chat_display.config(state=tk.DISABLED)

                # Add real response
                self.add_message("🤖 AI Assistant", response)

            except Exception as e:
                self.add_message("❌ Error", f"Failed to get response: {str(e)}")

        threading.Thread(target=get_response, daemon=True).start()
    
    def on_enter(self, event):
        """Handle Enter key"""
        self.send_message()
        return "break"
    
    def on_shift_enter(self, event):
        """Handle Shift+Enter for new line"""
        return None

    def update_status(self):
        """Update the status bar with current target progress"""
        progress = self.bot.target_manager.get_progress_info()

        if progress["status"] == "NO_TARGET":
            self.status_label.config(
                text="🎯 No active target - Say 'Set target $500 in 1 day' to get started!",
                fg='#ffffff'
            )
        else:
            urgency_colors = {
                "CRITICAL": "#ff4444",
                "HIGH": "#ff8800",
                "MODERATE": "#ffaa00",
                "LOW": "#00aa00"
            }

            urgency_emojis = {
                "CRITICAL": "🚨",
                "HIGH": "⚡",
                "MODERATE": "📊",
                "LOW": "😌"
            }

            urgency = progress["urgency_level"]
            emoji = urgency_emojis.get(urgency, "📊")
            color = urgency_colors.get(urgency, "#ffffff")

            status_text = f"{emoji} ${progress['current_pnl']:.2f}/${progress['target_amount']:.2f} ({progress['progress_percentage']:.1f}%) | {progress['timeframe_description']} | {progress['remaining_time_minutes']:.1f}m left | {urgency} URGENCY"

            self.status_label.config(text=status_text, fg=color)

    def update_status_timer(self):
        """Timer function to update status periodically"""
        self.update_status()
        self.root.after(5000, self.update_status_timer)  # Update every 5 seconds

    def setup_api_key(self):
        """Setup OpenAI API key"""
        api_key = simpledialog.askstring(
            "OpenAI API Key",
            "Enter your OpenAI API key:",
            show='*'
        )
        
        if api_key:
            global OPENAI_API_KEY
            OPENAI_API_KEY = api_key
            self.add_message("✅ System", "API key configured! You can now chat with the AI.")
        else:
            self.add_message("⚠️ System", "API key required for AI responses.")
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Launch ChatGPT-style trading bot with flexible targeting"""

    print("🚀 Launching ChatGPT Trading Bot with FLEXIBLE PROFIT TARGETING...")
    print("🎯 Set ANY profit target with ANY timeframe!")
    print("📊 Real AI conversations about trading and markets")
    print("💰 Examples: 'Set target $50 in 30 minutes' or 'New target $500 in 1 day'")

    app = ChatGPTTradingGUI()
    app.run()

if __name__ == "__main__":
    main()
