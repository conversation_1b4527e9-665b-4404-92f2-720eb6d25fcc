#!/usr/bin/env python3
"""
ChatGPT-Style Trading Bot
Real OpenAI API integration for intelligent trading conversations
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox, simpledialog, ttk
import threading
import time
from datetime import datetime, timedelta
import yfinance as yf
import requests
import json
import os
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Tuple
from dataclasses import dataclass
from enum import Enum
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
import asyncio
from dotenv import load_dotenv

# Import Alpaca trading components
try:
    from alpaca.trading.client import TradingClient
    from alpaca.trading.requests import MarketOrderRequest, LimitOrderRequest, StopOrderRequest
    from alpaca.trading.enums import OrderSide, TimeInForce, OrderType, OrderClass
    from alpaca.common.exceptions import APIError
    ALPACA_AVAILABLE = True
except ImportError:
    print("⚠️ Alpaca SDK not installed. Install with: pip install alpaca-py")
    ALPACA_AVAILABLE = False

# Load environment variables
load_dotenv()

# OpenAI API Configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

# Flexible Profit Targeting System
class TimeframeUnit(Enum):
    MINUTES = "minutes"
    HOURS = "hours"
    DAYS = "days"
    WEEKS = "weeks"
    MONTHS = "months"

@dataclass
class ProfitTarget:
    amount: float
    timeframe_value: int
    timeframe_unit: TimeframeUnit
    start_time: datetime
    end_time: datetime
    current_pnl: float = 0.0

    @property
    def remaining_amount(self) -> float:
        return max(0, self.amount - self.current_pnl)

    @property
    def remaining_time_seconds(self) -> float:
        return max(0, (self.end_time - datetime.now()).total_seconds())

    @property
    def remaining_time_minutes(self) -> float:
        return self.remaining_time_seconds / 60

    @property
    def progress_percentage(self) -> float:
        return min(100, (self.current_pnl / self.amount) * 100) if self.amount > 0 else 0

    @property
    def time_elapsed_percentage(self) -> float:
        total_seconds = (self.end_time - self.start_time).total_seconds()
        elapsed_seconds = (datetime.now() - self.start_time).total_seconds()
        return min(100, (elapsed_seconds / total_seconds) * 100) if total_seconds > 0 else 0

    @property
    def urgency_level(self) -> str:
        time_remaining_pct = 100 - self.time_elapsed_percentage
        progress_pct = self.progress_percentage

        if time_remaining_pct < 10 and progress_pct < 50:
            return "CRITICAL"
        elif time_remaining_pct < 25 and progress_pct < 75:
            return "HIGH"
        elif time_remaining_pct < 50:
            return "MODERATE"
        else:
            return "LOW"

    @property
    def is_expired(self) -> bool:
        return datetime.now() >= self.end_time

    @property
    def is_achieved(self) -> bool:
        return self.current_pnl >= self.amount

    def get_required_profit_rate(self) -> float:
        """Get required profit per minute to achieve target"""
        remaining_minutes = self.remaining_time_minutes
        if remaining_minutes <= 0:
            return float('inf')
        return self.remaining_amount / remaining_minutes

    def get_timeframe_description(self) -> str:
        """Get human-readable timeframe description"""
        if self.timeframe_value == 1:
            unit_name = self.timeframe_unit.value[:-1]  # Remove 's'
        else:
            unit_name = self.timeframe_unit.value

        return f"{self.timeframe_value} {unit_name}"

    def get_status_summary(self) -> str:
        """Get comprehensive status summary"""
        return f"${self.current_pnl:.2f}/${self.amount:.2f} ({self.progress_percentage:.1f}%) in {self.get_timeframe_description()}"

class FlexibleTargetManager:
    """Manages flexible profit targets"""

    def __init__(self):
        self.current_target: Optional[ProfitTarget] = None
        self.trades_completed = 0

    def set_target(self, amount: float, timeframe_value: int, timeframe_unit: TimeframeUnit) -> ProfitTarget:
        """Set a new profit target"""
        start_time = datetime.now()

        # Calculate end time based on timeframe
        if timeframe_unit == TimeframeUnit.MINUTES:
            end_time = start_time + timedelta(minutes=timeframe_value)
        elif timeframe_unit == TimeframeUnit.HOURS:
            end_time = start_time + timedelta(hours=timeframe_value)
        elif timeframe_unit == TimeframeUnit.DAYS:
            end_time = start_time + timedelta(days=timeframe_value)
        elif timeframe_unit == TimeframeUnit.WEEKS:
            end_time = start_time + timedelta(weeks=timeframe_value)
        elif timeframe_unit == TimeframeUnit.MONTHS:
            end_time = start_time + timedelta(days=timeframe_value * 30)  # Approximate
        else:
            raise ValueError(f"Unsupported timeframe unit: {timeframe_unit}")

        self.current_target = ProfitTarget(
            amount=amount,
            timeframe_value=timeframe_value,
            timeframe_unit=timeframe_unit,
            start_time=start_time,
            end_time=end_time
        )

        self.trades_completed = 0
        return self.current_target

    def update_pnl(self, trade_pnl: float):
        """Update P&L for current target"""
        if self.current_target:
            self.current_target.current_pnl += trade_pnl
            self.trades_completed += 1

    def get_progress_info(self) -> dict:
        """Get current progress information"""
        if not self.current_target:
            return {"status": "NO_TARGET", "message": "No active profit target set"}

        target = self.current_target
        return {
            "target_amount": target.amount,
            "current_pnl": target.current_pnl,
            "remaining_amount": target.remaining_amount,
            "progress_percentage": target.progress_percentage,
            "timeframe_description": target.get_timeframe_description(),
            "time_elapsed_percentage": target.time_elapsed_percentage,
            "remaining_time_minutes": target.remaining_time_minutes,
            "urgency_level": target.urgency_level,
            "required_profit_rate": target.get_required_profit_rate(),
            "trades_completed": self.trades_completed,
            "status_summary": target.get_status_summary(),
            "is_achieved": target.is_achieved,
            "is_expired": target.is_expired,
            "status": "ACTIVE"
        }

@dataclass
class TradingOpportunity:
    """Represents a specific trading opportunity with actionable details"""
    symbol: str
    company_name: str
    current_price: float
    entry_price: float
    exit_target: float
    stop_loss: float
    position_size: int
    risk_reward_ratio: float
    probability_score: float
    strategy_type: str  # "SCALP", "DAY_TRADE", "SWING"
    timeframe: str
    technical_rationale: str
    execution_method: str  # "MARKET", "LIMIT", "STOP_LIMIT"
    urgency_level: str
    sector: str
    volume_analysis: str

    @property
    def profit_potential(self) -> float:
        return (self.exit_target - self.entry_price) * self.position_size

    @property
    def risk_amount(self) -> float:
        return (self.entry_price - self.stop_loss) * self.position_size

    def get_formatted_output(self) -> str:
        return f"""
🎯 **{self.symbol} - {self.company_name}**
📊 **Current Price**: ${self.current_price:.2f}
🟢 **Entry**: ${self.entry_price:.2f}
🎯 **Target**: ${self.exit_target:.2f} (+{((self.exit_target/self.entry_price-1)*100):.1f}%)
🔴 **Stop Loss**: ${self.stop_loss:.2f} (-{((1-self.stop_loss/self.entry_price)*100):.1f}%)
📈 **Position Size**: {self.position_size} shares
💰 **Profit Potential**: ${self.profit_potential:.2f}
⚠️ **Risk Amount**: ${self.risk_amount:.2f}
📊 **Risk/Reward**: 1:{self.risk_reward_ratio:.2f}
🎲 **Success Probability**: {self.probability_score:.0f}%
⚡ **Strategy**: {self.strategy_type} ({self.timeframe})
🏭 **Sector**: {self.sector}
📋 **Execution**: {self.execution_method} order
🔍 **Technical Rationale**: {self.technical_rationale}
📊 **Volume**: {self.volume_analysis}
"""

class RealTimeMarketAnalyzer:
    """Advanced real-time market analysis engine for actionable trading insights"""

    def __init__(self):
        self.sp500_symbols = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'BRK-B', 'UNH', 'JNJ',
            'V', 'PG', 'JPM', 'HD', 'CVX', 'MA', 'ABBV', 'PFE', 'AVGO', 'KO',
            'COST', 'PEP', 'TMO', 'WMT', 'BAC', 'DIS', 'ABT', 'CRM', 'ACN', 'VZ',
            'ADBE', 'CMCSA', 'DHR', 'NKE', 'TXN', 'NEE', 'RTX', 'QCOM', 'PM', 'UPS',
            'LOW', 'ORCL', 'HON', 'T', 'IBM', 'AMGN', 'SPGI', 'CAT', 'GS', 'INTU'
        ]
        self.cache = {}
        self.cache_timeout = 60  # 1 minute cache

    def calculate_technical_indicators(self, symbol: str, period: str = "1mo") -> Dict:
        """Calculate comprehensive technical indicators for a symbol"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval="1d")

            if hist.empty:
                return {}

            # Price data
            current_price = hist['Close'].iloc[-1]

            # Moving averages
            sma_20 = hist['Close'].rolling(20).mean().iloc[-1] if len(hist) >= 20 else current_price
            sma_50 = hist['Close'].rolling(50).mean().iloc[-1] if len(hist) >= 50 else current_price
            ema_12 = hist['Close'].ewm(span=12).mean().iloc[-1]
            ema_26 = hist['Close'].ewm(span=26).mean().iloc[-1]

            # RSI calculation
            delta = hist['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs)).iloc[-1]

            # MACD
            macd = ema_12 - ema_26
            macd_signal = macd.ewm(span=9).mean().iloc[-1]
            macd_histogram = (macd - macd.ewm(span=9).mean()).iloc[-1]

            # Volume analysis
            avg_volume = hist['Volume'].rolling(20).mean().iloc[-1]
            current_volume = hist['Volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

            # Support and resistance
            high_20 = hist['High'].rolling(20).max().iloc[-1]
            low_20 = hist['Low'].rolling(20).min().iloc[-1]

            # Bollinger Bands
            bb_middle = sma_20
            bb_std = hist['Close'].rolling(20).std().iloc[-1]
            bb_upper = bb_middle + (bb_std * 2)
            bb_lower = bb_middle - (bb_std * 2)

            return {
                'current_price': current_price,
                'sma_20': sma_20,
                'sma_50': sma_50,
                'ema_12': ema_12,
                'ema_26': ema_26,
                'rsi': rsi,
                'macd': macd.iloc[-1],
                'macd_signal': macd_signal,
                'macd_histogram': macd_histogram,
                'volume_ratio': volume_ratio,
                'avg_volume': avg_volume,
                'current_volume': current_volume,
                'resistance': high_20,
                'support': low_20,
                'bb_upper': bb_upper,
                'bb_lower': bb_lower,
                'bb_middle': bb_middle
            }

        except Exception as e:
            print(f"Error calculating indicators for {symbol}: {e}")
            return {}

    def get_stock_fundamentals(self, symbol: str) -> Dict:
        """Get fundamental data for a stock"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info

            return {
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE'),
                'sector': info.get('sector', 'Unknown'),
                'industry': info.get('industry', 'Unknown'),
                'company_name': info.get('longName', symbol),
                'beta': info.get('beta'),
                'dividend_yield': info.get('dividendYield'),
                'profit_margins': info.get('profitMargins'),
                'revenue_growth': info.get('revenueGrowth')
            }
        except Exception as e:
            print(f"Error getting fundamentals for {symbol}: {e}")
            return {'company_name': symbol, 'sector': 'Unknown'}

    def generate_trading_opportunity(self, symbol: str, target_amount: float,
                                   urgency_level: str, timeframe_minutes: float) -> Optional[TradingOpportunity]:
        """Generate a specific trading opportunity based on analysis and target parameters"""
        try:
            # Get technical and fundamental data
            indicators = self.calculate_technical_indicators(symbol)
            fundamentals = self.get_stock_fundamentals(symbol)

            if not indicators:
                return None

            current_price = indicators['current_price']
            rsi = indicators['rsi']
            volume_ratio = indicators['volume_ratio']

            # Determine strategy type based on urgency and timeframe
            if urgency_level == "CRITICAL" or timeframe_minutes <= 30:
                strategy_type = "SCALP"
                timeframe_desc = "1-5 minutes"
                execution_method = "MARKET"
            elif urgency_level == "HIGH" or timeframe_minutes <= 240:
                strategy_type = "DAY_TRADE"
                timeframe_desc = "15-60 minutes"
                execution_method = "LIMIT"
            else:
                strategy_type = "SWING"
                timeframe_desc = "1-5 days"
                execution_method = "LIMIT"

            # Calculate entry, exit, and stop loss based on strategy
            if strategy_type == "SCALP":
                # Tight scalping parameters
                entry_price = current_price * 1.001  # Slight premium for immediate entry
                exit_target = current_price * 1.005  # 0.5% target
                stop_loss = current_price * 0.997   # 0.3% stop
                risk_reward = 1.67
            elif strategy_type == "DAY_TRADE":
                # Day trading parameters
                entry_price = current_price * 1.002
                exit_target = current_price * 1.015  # 1.5% target
                stop_loss = current_price * 0.992   # 0.8% stop
                risk_reward = 1.88
            else:
                # Swing trading parameters
                entry_price = current_price * 1.005
                exit_target = current_price * 1.035  # 3.5% target
                stop_loss = current_price * 0.985   # 1.5% stop
                risk_reward = 2.33

            # Calculate position size based on target amount and risk
            risk_per_share = entry_price - stop_loss
            max_risk = target_amount * 0.02  # Risk 2% of target
            position_size = max(1, int(max_risk / risk_per_share)) if risk_per_share > 0 else 100

            # Generate probability score based on technical indicators
            probability_score = self.calculate_probability_score(indicators, strategy_type)

            # Generate technical rationale
            technical_rationale = self.generate_technical_rationale(indicators, strategy_type)

            # Volume analysis
            if volume_ratio > 1.5:
                volume_analysis = f"High volume ({volume_ratio:.1f}x avg) - Strong interest"
            elif volume_ratio < 0.7:
                volume_analysis = f"Low volume ({volume_ratio:.1f}x avg) - Weak conviction"
            else:
                volume_analysis = f"Normal volume ({volume_ratio:.1f}x avg) - Steady flow"

            return TradingOpportunity(
                symbol=symbol,
                company_name=fundamentals.get('company_name', symbol),
                current_price=current_price,
                entry_price=entry_price,
                exit_target=exit_target,
                stop_loss=stop_loss,
                position_size=position_size,
                risk_reward_ratio=risk_reward,
                probability_score=probability_score,
                strategy_type=strategy_type,
                timeframe=timeframe_desc,
                technical_rationale=technical_rationale,
                execution_method=execution_method,
                urgency_level=urgency_level,
                sector=fundamentals.get('sector', 'Unknown'),
                volume_analysis=volume_analysis
            )

        except Exception as e:
            print(f"Error generating opportunity for {symbol}: {e}")
            return None

    def calculate_probability_score(self, indicators: Dict, strategy_type: str) -> float:
        """Calculate probability of success based on technical indicators"""
        score = 50.0  # Base score

        rsi = indicators.get('rsi', 50)
        volume_ratio = indicators.get('volume_ratio', 1)
        current_price = indicators.get('current_price', 0)
        sma_20 = indicators.get('sma_20', current_price)
        sma_50 = indicators.get('sma_50', current_price)

        # RSI scoring
        if 30 <= rsi <= 70:
            score += 15  # Neutral RSI is good
        elif rsi < 30:
            score += 10  # Oversold can be opportunity
        elif rsi > 70:
            score += 5   # Overbought is risky

        # Trend scoring
        if current_price > sma_20 > sma_50:
            score += 20  # Strong uptrend
        elif current_price > sma_20:
            score += 10  # Mild uptrend

        # Volume scoring
        if volume_ratio > 1.5:
            score += 15  # High volume is good
        elif volume_ratio > 1.2:
            score += 10
        elif volume_ratio < 0.8:
            score -= 10  # Low volume is concerning

        # Strategy-specific adjustments
        if strategy_type == "SCALP":
            if volume_ratio > 2.0:
                score += 10  # High volume crucial for scalping
        elif strategy_type == "SWING":
            if current_price > sma_50:
                score += 10  # Long-term trend important for swings

        return min(95, max(15, score))  # Cap between 15-95%

    def generate_technical_rationale(self, indicators: Dict, strategy_type: str) -> str:
        """Generate human-readable technical rationale"""
        rationales = []

        rsi = indicators.get('rsi', 50)
        volume_ratio = indicators.get('volume_ratio', 1)
        current_price = indicators.get('current_price', 0)
        sma_20 = indicators.get('sma_20', current_price)
        macd_histogram = indicators.get('macd_histogram', 0)

        # RSI analysis
        if rsi < 30:
            rationales.append("RSI oversold - potential bounce")
        elif rsi > 70:
            rationales.append("RSI overbought - momentum play")
        else:
            rationales.append("RSI neutral - balanced conditions")

        # Trend analysis
        if current_price > sma_20:
            rationales.append("Above 20-day MA - bullish trend")
        else:
            rationales.append("Below 20-day MA - bearish pressure")

        # MACD analysis
        if macd_histogram > 0:
            rationales.append("MACD positive - upward momentum")
        else:
            rationales.append("MACD negative - downward pressure")

        # Volume analysis
        if volume_ratio > 1.5:
            rationales.append("High volume confirms move")

        return "; ".join(rationales)

    def scan_market_opportunities(self, target_amount: float, urgency_level: str,
                                timeframe_minutes: float, max_opportunities: int = 5) -> List[TradingOpportunity]:
        """Scan the market for the best trading opportunities"""
        opportunities = []

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=10) as executor:
            # Submit tasks for top symbols
            future_to_symbol = {
                executor.submit(self.generate_trading_opportunity, symbol, target_amount,
                              urgency_level, timeframe_minutes): symbol
                for symbol in self.sp500_symbols[:30]  # Scan top 30 for speed
            }

            for future in concurrent.futures.as_completed(future_to_symbol):
                try:
                    opportunity = future.result(timeout=5)
                    if opportunity and opportunity.probability_score > 60:
                        opportunities.append(opportunity)
                except Exception as e:
                    symbol = future_to_symbol[future]
                    print(f"Error processing {symbol}: {e}")

        # Sort by probability score and return top opportunities
        opportunities.sort(key=lambda x: x.probability_score, reverse=True)
        return opportunities[:max_opportunities]

@dataclass
class TradeConfirmation:
    """Represents a trade that needs user confirmation"""
    opportunity: TradingOpportunity
    account_balance: float
    current_positions: List[Dict]
    risk_assessment: str
    confirmation_message: str

    def get_confirmation_prompt(self) -> str:
        return f"""
🚨 **TRADE CONFIRMATION REQUIRED** 🚨

{self.opportunity.get_formatted_output()}

💰 **Account Status**:
• Available Balance: ${self.account_balance:,.2f}
• Current Positions: {len(self.current_positions)}

⚠️ **Risk Assessment**: {self.risk_assessment}

🎯 **EXECUTE THIS TRADE?**
{self.confirmation_message}

Type 'yes' or 'y' to execute, 'no' or 'n' to cancel.
"""

class AlpacaTradingIntegration:
    """Alpaca trading API integration with safety controls"""

    def __init__(self):
        self.client = None
        self.paper_trading = True
        self.max_daily_loss = 500.0  # Maximum daily loss limit
        self.max_position_size = 1000.0  # Maximum position size
        self.daily_trades = 0
        self.daily_pnl = 0.0
        self.pending_confirmations = {}
        self.active_orders = {}

        # Initialize Alpaca client if available
        if ALPACA_AVAILABLE:
            self.initialize_alpaca()

    def initialize_alpaca(self):
        """Initialize Alpaca trading client"""
        try:
            # Get API credentials from environment
            api_key = os.getenv("ALPACA_API_KEY")
            secret_key = os.getenv("ALPACA_SECRET_KEY")

            if not api_key or not secret_key:
                print("⚠️ Alpaca API credentials not found in environment variables")
                return False

            # Initialize client (paper trading by default)
            self.client = TradingClient(
                api_key=api_key,
                secret_key=secret_key,
                paper=self.paper_trading
            )

            # Test connection
            account = self.client.get_account()
            print(f"✅ Connected to Alpaca {'Paper' if self.paper_trading else 'Live'} Trading")
            print(f"📊 Account Status: {account.status}")
            print(f"💰 Buying Power: ${float(account.buying_power):,.2f}")

            return True

        except Exception as e:
            print(f"❌ Failed to initialize Alpaca: {str(e)}")
            return False

    def get_account_info(self) -> Dict:
        """Get current account information"""
        if not self.client:
            return {"error": "Alpaca client not initialized"}

        try:
            account = self.client.get_account()
            return {
                "account_id": account.id,
                "status": account.status.value,
                "equity": float(account.equity),
                "buying_power": float(account.buying_power),
                "cash": float(account.cash),
                "portfolio_value": float(account.portfolio_value),
                "day_trade_count": account.daytrade_count,
                "pattern_day_trader": account.pattern_day_trader,
                "paper_trading": self.paper_trading
            }
        except Exception as e:
            return {"error": f"Failed to get account info: {str(e)}"}

    def get_positions(self) -> List[Dict]:
        """Get current positions"""
        if not self.client:
            return []

        try:
            positions = self.client.get_all_positions()
            position_list = []

            for pos in positions:
                position_list.append({
                    "symbol": pos.symbol,
                    "quantity": float(pos.qty),
                    "side": "long" if float(pos.qty) > 0 else "short",
                    "market_value": float(pos.market_value) if pos.market_value else 0.0,
                    "unrealized_pl": float(pos.unrealized_pl) if pos.unrealized_pl else 0.0,
                    "unrealized_plpc": float(pos.unrealized_plpc) if pos.unrealized_plpc else 0.0,
                    "avg_entry_price": float(pos.avg_entry_price) if pos.avg_entry_price else 0.0
                })

            return position_list
        except Exception as e:
            print(f"Error getting positions: {e}")
            return []

    def calculate_position_size(self, opportunity: TradingOpportunity, account_balance: float) -> int:
        """Calculate appropriate position size with risk management"""
        try:
            # Risk management parameters
            max_risk_per_trade = min(account_balance * 0.02, 100.0)  # 2% of account or $100 max
            risk_per_share = opportunity.entry_price - opportunity.stop_loss

            if risk_per_share <= 0:
                return 0

            # Calculate position size based on risk
            calculated_size = int(max_risk_per_trade / risk_per_share)

            # Apply maximum position size limit
            max_shares_by_value = int(self.max_position_size / opportunity.entry_price)

            # Use the smaller of the two
            position_size = min(calculated_size, max_shares_by_value, opportunity.position_size)

            return max(1, position_size)  # Minimum 1 share

        except Exception as e:
            print(f"Error calculating position size: {e}")
            return 0

    def assess_trade_risk(self, opportunity: TradingOpportunity, account_info: Dict) -> str:
        """Assess the risk of a potential trade"""
        risk_factors = []

        # Check daily loss limit
        if self.daily_pnl < -self.max_daily_loss:
            risk_factors.append("🔴 Daily loss limit exceeded")

        # Check buying power
        trade_value = opportunity.entry_price * opportunity.position_size
        if trade_value > account_info.get("buying_power", 0):
            risk_factors.append("🔴 Insufficient buying power")

        # Check probability score
        if opportunity.probability_score < 70:
            risk_factors.append("🟡 Below 70% probability threshold")

        # Check if it's a high-risk time
        if opportunity.urgency_level == "CRITICAL":
            risk_factors.append("🟡 Critical urgency - higher risk")

        if not risk_factors:
            return "🟢 LOW RISK - All safety checks passed"
        elif len(risk_factors) == 1 and "🟡" in risk_factors[0]:
            return f"🟡 MODERATE RISK - {risk_factors[0]}"
        else:
            return f"🔴 HIGH RISK - {'; '.join(risk_factors)}"

    def create_trade_confirmation(self, opportunity: TradingOpportunity) -> TradeConfirmation:
        """Create a trade confirmation object"""
        account_info = self.get_account_info()
        positions = self.get_positions()

        # Calculate appropriate position size
        adjusted_position_size = self.calculate_position_size(
            opportunity, account_info.get("buying_power", 0)
        )

        # Update opportunity with adjusted position size
        opportunity.position_size = adjusted_position_size

        # Assess risk
        risk_assessment = self.assess_trade_risk(opportunity, account_info)

        # Create confirmation message
        confirmation_message = f"""
📋 **Trade Details**:
• Symbol: {opportunity.symbol}
• Action: BUY {opportunity.position_size} shares
• Entry Price: ${opportunity.entry_price:.2f}
• Target Price: ${opportunity.exit_target:.2f}
• Stop Loss: ${opportunity.stop_loss:.2f}
• Estimated Cost: ${opportunity.entry_price * opportunity.position_size:.2f}
• Profit Potential: ${opportunity.profit_potential:.2f}
• Risk Amount: ${opportunity.risk_amount:.2f}
"""

        return TradeConfirmation(
            opportunity=opportunity,
            account_balance=account_info.get("buying_power", 0),
            current_positions=positions,
            risk_assessment=risk_assessment,
            confirmation_message=confirmation_message
        )

    def execute_trade(self, opportunity: TradingOpportunity) -> Dict:
        """Execute a trade after confirmation"""
        if not self.client:
            return {"success": False, "error": "Alpaca client not initialized"}

        try:
            # Determine order type based on execution method
            if opportunity.execution_method == "MARKET":
                order_result = self.place_market_order(
                    opportunity.symbol,
                    "buy",
                    opportunity.position_size
                )
            else:  # LIMIT order
                order_result = self.place_limit_order(
                    opportunity.symbol,
                    "buy",
                    opportunity.position_size,
                    opportunity.entry_price
                )

            if order_result and order_result.get("success"):
                # Store order for tracking
                order_id = order_result["order_id"]
                self.active_orders[order_id] = {
                    "opportunity": opportunity,
                    "order_type": "entry",
                    "timestamp": datetime.now()
                }

                # Schedule stop-loss order (will be placed after entry fills)
                self.schedule_stop_loss(opportunity, order_id)

                return {
                    "success": True,
                    "order_id": order_id,
                    "message": f"✅ Entry order placed for {opportunity.symbol}",
                    "order_details": order_result
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to place entry order: {order_result.get('error', 'Unknown error')}"
                }

        except Exception as e:
            return {"success": False, "error": f"Trade execution error: {str(e)}"}

    def place_market_order(self, symbol: str, side: str, quantity: int) -> Dict:
        """Place a market order"""
        try:
            order_side = OrderSide.BUY if side.upper() == "BUY" else OrderSide.SELL

            market_order_data = MarketOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=order_side,
                time_in_force=TimeInForce.DAY
            )

            order = self.client.submit_order(order_data=market_order_data)

            return {
                "success": True,
                "order_id": str(order.id),
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "type": "market",
                "status": order.status.value,
                "submitted_at": order.submitted_at
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def place_limit_order(self, symbol: str, side: str, quantity: int, limit_price: float) -> Dict:
        """Place a limit order"""
        try:
            order_side = OrderSide.BUY if side.upper() == "BUY" else OrderSide.SELL

            limit_order_data = LimitOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=order_side,
                time_in_force=TimeInForce.DAY,
                limit_price=limit_price
            )

            order = self.client.submit_order(order_data=limit_order_data)

            return {
                "success": True,
                "order_id": str(order.id),
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "type": "limit",
                "limit_price": limit_price,
                "status": order.status.value,
                "submitted_at": order.submitted_at
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def schedule_stop_loss(self, opportunity: TradingOpportunity, entry_order_id: str):
        """Schedule stop-loss order to be placed after entry fills"""
        # This would typically be handled by monitoring order fills
        # For now, we'll store it for manual placement
        self.active_orders[f"{entry_order_id}_stop"] = {
            "type": "pending_stop_loss",
            "symbol": opportunity.symbol,
            "quantity": opportunity.position_size,
            "stop_price": opportunity.stop_loss,
            "parent_order": entry_order_id
        }

    def place_stop_loss_order(self, symbol: str, quantity: int, stop_price: float) -> Dict:
        """Place a stop-loss order"""
        try:
            stop_order_data = StopOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=OrderSide.SELL,
                time_in_force=TimeInForce.DAY,
                stop_price=stop_price
            )

            order = self.client.submit_order(order_data=stop_order_data)

            return {
                "success": True,
                "order_id": str(order.id),
                "symbol": symbol,
                "quantity": quantity,
                "type": "stop",
                "stop_price": stop_price,
                "status": order.status.value
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_order_status(self, order_id: str) -> Dict:
        """Get order status"""
        try:
            order = self.client.get_order_by_id(order_id)
            return {
                "id": str(order.id),
                "status": order.status.value,
                "filled_qty": float(order.filled_qty) if order.filled_qty else 0,
                "filled_avg_price": float(order.filled_avg_price) if order.filled_avg_price else 0.0,
                "filled_at": order.filled_at
            }
        except Exception as e:
            return {"error": f"Failed to get order status: {str(e)}"}

    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            self.client.cancel_order_by_id(order_id)
            return True
        except Exception as e:
            print(f"Error cancelling order {order_id}: {e}")
            return False

    def get_daily_pnl(self) -> float:
        """Calculate daily P&L from positions and closed trades"""
        try:
            positions = self.get_positions()
            daily_pnl = sum(pos["unrealized_pl"] for pos in positions)
            return daily_pnl
        except Exception as e:
            print(f"Error calculating daily P&L: {e}")
            return 0.0

class VolatilityAnalyzer:
    """Advanced volatility analysis using StdDev/ATR ratio"""

    def __init__(self):
        self.std_dev_length = 20
        self.atr_length = 14

    def calculate_volatility_ratio(self, symbol: str, period: str = "3mo") -> Dict:
        """Calculate volatility ratio: 2 * StdDev / ATR"""
        try:
            ticker = yf.Ticker(symbol)
            df = ticker.history(period=period, interval="1d")

            if len(df) < max(self.std_dev_length, self.atr_length):
                return {"error": f"Insufficient data for {symbol}"}

            # Standard Deviation
            df['StdDev'] = df['Close'].rolling(window=self.std_dev_length).std()

            # True Range & ATR
            df['H-L'] = df['High'] - df['Low']
            df['H-PC'] = np.abs(df['High'] - df['Close'].shift(1))
            df['L-PC'] = np.abs(df['Low'] - df['Close'].shift(1))
            df['TrueRange'] = df[['H-L', 'H-PC', 'L-PC']].max(axis=1)
            df['ATR'] = df['TrueRange'].rolling(window=self.atr_length).mean()

            # Volatility Ratio
            df['Ratio'] = (2 * df['StdDev']) / df['ATR']

            # Get current values
            current_ratio = df['Ratio'].iloc[-1]
            current_stddev = df['StdDev'].iloc[-1]
            current_atr = df['ATR'].iloc[-1]

            # Determine volatility condition
            if current_ratio < 1:
                condition = "EXTREMELY_LOW"
                color = "orange"
                signal = "High volatility expected - Caution"
            elif current_ratio < 1.5:
                condition = "LOW"
                color = "red"
                signal = "Increased volatility likely - Risk management critical"
            elif current_ratio < 2:
                condition = "MODERATE"
                color = "gray"
                signal = "Normal volatility - Standard trading conditions"
            elif current_ratio > 4:
                condition = "VERY_HIGH"
                color = "yellow"
                signal = "Very low volatility - Breakout potential"
            else:
                condition = "HIGH"
                color = "green"
                signal = "Low volatility - Good for range trading"

            return {
                "symbol": symbol,
                "current_ratio": current_ratio,
                "stddev": current_stddev,
                "atr": current_atr,
                "condition": condition,
                "color": color,
                "signal": signal,
                "interpretation": self.interpret_ratio(current_ratio),
                "trading_recommendation": self.get_trading_recommendation(current_ratio)
            }

        except Exception as e:
            return {"error": f"Error calculating volatility for {symbol}: {str(e)}"}

    def interpret_ratio(self, ratio: float) -> str:
        """Interpret the volatility ratio"""
        if ratio < 1:
            return "Market is in high volatility state. Expect large price movements."
        elif ratio < 1.5:
            return "Volatility is elevated. Use tighter stops and smaller positions."
        elif ratio < 2:
            return "Normal volatility conditions. Standard trading strategies apply."
        elif ratio > 4:
            return "Very low volatility. Market may be coiling for a breakout."
        else:
            return "Low volatility environment. Good for range-bound strategies."

    def get_trading_recommendation(self, ratio: float) -> str:
        """Get trading recommendations based on volatility ratio"""
        if ratio < 1:
            return "🔴 HIGH RISK: Use smaller positions, wider stops, expect whipsaws"
        elif ratio < 1.5:
            return "🟡 MODERATE RISK: Reduce position size, use trailing stops"
        elif ratio < 2:
            return "🟢 NORMAL RISK: Standard position sizing and risk management"
        elif ratio > 4:
            return "🟡 BREAKOUT WATCH: Position for potential volatility expansion"
        else:
            return "🟢 LOW RISK: Good for range trading and mean reversion"

    def scan_volatility_conditions(self, symbols: List[str]) -> List[Dict]:
        """Scan multiple symbols for volatility conditions"""
        results = []

        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_symbol = {
                executor.submit(self.calculate_volatility_ratio, symbol): symbol
                for symbol in symbols
            }

            for future in concurrent.futures.as_completed(future_to_symbol):
                try:
                    result = future.result(timeout=5)
                    if "error" not in result:
                        results.append(result)
                except Exception as e:
                    symbol = future_to_symbol[future]
                    print(f"Error processing volatility for {symbol}: {e}")

        # Sort by ratio (most interesting conditions first)
        results.sort(key=lambda x: abs(x['current_ratio'] - 2))  # Sort by deviation from normal
        return results

@dataclass
class TTMSqueezeData:
    """Represents TTM Squeeze analysis data for a stock"""
    symbol: str
    squeeze_status: str  # "IN_SQUEEZE", "FIRED", "BUILDING", "NORMAL"
    squeeze_strength: float  # 0-100 scale
    momentum_direction: str  # "BULLISH", "BEARISH", "NEUTRAL"
    momentum_value: float
    bb_squeeze: bool  # Bollinger Bands inside Keltner Channels
    kc_squeeze: bool  # Keltner Channels compression
    squeeze_duration: int  # Days in squeeze
    breakout_probability: float  # 0-100 probability of breakout
    entry_price: float
    target_price: float
    stop_loss: float
    risk_reward_ratio: float
    volume_confirmation: bool
    squeeze_color: str  # Visual indicator color

    def get_formatted_output(self) -> str:
        status_emoji = {
            "IN_SQUEEZE": "🔥",
            "FIRED": "🚀",
            "BUILDING": "⚡",
            "NORMAL": "📊"
        }.get(self.squeeze_status, "📊")

        momentum_emoji = {
            "BULLISH": "🟢",
            "BEARISH": "🔴",
            "NEUTRAL": "🟡"
        }.get(self.momentum_direction, "🟡")

        return f"""
{status_emoji} **{self.symbol} - TTM SQUEEZE ANALYSIS**
🎯 **Status**: {self.squeeze_status} ({self.squeeze_strength:.0f}% strength)
{momentum_emoji} **Momentum**: {self.momentum_direction} ({self.momentum_value:+.3f})
⏰ **Duration**: {self.squeeze_duration} days in squeeze
🎲 **Breakout Probability**: {self.breakout_probability:.0f}%
💰 **Entry**: ${self.entry_price:.2f}
🎯 **Target**: ${self.target_price:.2f} (+{((self.target_price/self.entry_price-1)*100):.1f}%)
🔴 **Stop**: ${self.stop_loss:.2f} (-{((1-self.stop_loss/self.entry_price)*100):.1f}%)
📊 **Risk/Reward**: 1:{self.risk_reward_ratio:.2f}
📈 **Volume Confirmation**: {'✅' if self.volume_confirmation else '❌'}
🎨 **Squeeze Color**: {self.squeeze_color}
"""

class TTMSqueezeAnalyzer:
    """Advanced TTM Squeeze detection and analysis system"""

    def __init__(self):
        # TTM Squeeze parameters
        self.bb_length = 20
        self.bb_mult = 2.0
        self.kc_length = 20
        self.kc_mult = 1.5
        self.momentum_length = 12

        # Squeeze detection thresholds
        self.squeeze_threshold = 0.95  # BB must be 95% inside KC
        self.momentum_threshold = 0.001
        self.volume_threshold = 1.2  # 20% above average

    def calculate_ttm_squeeze(self, symbol: str, period: str = "6mo") -> Optional[TTMSqueezeData]:
        """Calculate comprehensive TTM Squeeze analysis"""
        try:
            ticker = yf.Ticker(symbol)
            df = ticker.history(period=period, interval="1d")

            if len(df) < max(self.bb_length, self.kc_length, self.momentum_length) + 10:
                return None

            # Calculate Bollinger Bands
            df['BB_Middle'] = df['Close'].rolling(self.bb_length).mean()
            df['BB_Std'] = df['Close'].rolling(self.bb_length).std()
            df['BB_Upper'] = df['BB_Middle'] + (df['BB_Std'] * self.bb_mult)
            df['BB_Lower'] = df['BB_Middle'] - (df['BB_Std'] * self.bb_mult)
            df['BB_Width'] = df['BB_Upper'] - df['BB_Lower']

            # Calculate Keltner Channels
            df['KC_Middle'] = df['Close'].rolling(self.kc_length).mean()
            df['TR'] = np.maximum(
                df['High'] - df['Low'],
                np.maximum(
                    abs(df['High'] - df['Close'].shift(1)),
                    abs(df['Low'] - df['Close'].shift(1))
                )
            )
            df['ATR'] = df['TR'].rolling(self.kc_length).mean()
            df['KC_Upper'] = df['KC_Middle'] + (df['ATR'] * self.kc_mult)
            df['KC_Lower'] = df['KC_Middle'] - (df['ATR'] * self.kc_mult)
            df['KC_Width'] = df['KC_Upper'] - df['KC_Lower']

            # TTM Squeeze Detection
            df['Squeeze'] = (df['BB_Upper'] <= df['KC_Upper']) & (df['BB_Lower'] >= df['KC_Lower'])

            # Momentum Oscillator
            highest_high = df['High'].rolling(self.momentum_length).max()
            lowest_low = df['Low'].rolling(self.momentum_length).min()
            df['Momentum'] = df['Close'] - ((highest_high + lowest_low) / 2)
            df['Momentum_MA'] = df['Momentum'].rolling(self.momentum_length).mean()

            # Volume analysis
            df['Volume_MA'] = df['Volume'].rolling(20).mean()
            df['Volume_Ratio'] = df['Volume'] / df['Volume_MA']

            # Get current values
            current_squeeze = df['Squeeze'].iloc[-1]
            current_momentum = df['Momentum'].iloc[-1]
            momentum_ma = df['Momentum_MA'].iloc[-1]
            current_price = df['Close'].iloc[-1]
            volume_ratio = df['Volume_Ratio'].iloc[-1]

            # Calculate squeeze duration
            squeeze_duration = self._calculate_squeeze_duration(df['Squeeze'])

            # Determine squeeze status
            squeeze_status, squeeze_strength = self._determine_squeeze_status(
                df, current_squeeze, squeeze_duration
            )

            # Determine momentum direction
            momentum_direction = self._determine_momentum_direction(
                current_momentum, momentum_ma, df['Momentum'].tail(5)
            )

            # Calculate breakout probability
            breakout_probability = self._calculate_breakout_probability(
                squeeze_duration, momentum_direction, volume_ratio, df
            )

            # Calculate entry, target, and stop levels
            entry_price, target_price, stop_loss = self._calculate_trade_levels(
                current_price, df, momentum_direction, squeeze_status
            )

            # Risk/reward calculation
            risk = abs(entry_price - stop_loss)
            reward = abs(target_price - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0

            # Volume confirmation
            volume_confirmation = volume_ratio >= self.volume_threshold

            # Squeeze color (visual indicator)
            squeeze_color = self._get_squeeze_color(
                current_squeeze, momentum_direction, squeeze_duration
            )

            return TTMSqueezeData(
                symbol=symbol,
                squeeze_status=squeeze_status,
                squeeze_strength=squeeze_strength,
                momentum_direction=momentum_direction,
                momentum_value=current_momentum,
                bb_squeeze=current_squeeze,
                kc_squeeze=current_squeeze,
                squeeze_duration=squeeze_duration,
                breakout_probability=breakout_probability,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                risk_reward_ratio=risk_reward_ratio,
                volume_confirmation=volume_confirmation,
                squeeze_color=squeeze_color
            )

        except Exception as e:
            print(f"Error calculating TTM Squeeze for {symbol}: {e}")
            return None

    def _calculate_squeeze_duration(self, squeeze_series) -> int:
        """Calculate how many consecutive days the stock has been in squeeze"""
        duration = 0
        for i in range(len(squeeze_series) - 1, -1, -1):
            if squeeze_series.iloc[i]:
                duration += 1
            else:
                break
        return duration

    def _determine_squeeze_status(self, df, current_squeeze: bool, duration: int) -> Tuple[str, float]:
        """Determine the current squeeze status and strength"""
        if current_squeeze:
            if duration >= 10:
                return "IN_SQUEEZE", min(100, duration * 5)  # Longer squeeze = higher strength
            else:
                return "BUILDING", duration * 10
        else:
            # Check if just fired (was in squeeze recently)
            recent_squeeze = df['Squeeze'].tail(5).any()
            if recent_squeeze and duration == 0:
                return "FIRED", 90
            else:
                return "NORMAL", 0

    def _determine_momentum_direction(self, current_momentum: float, momentum_ma: float,
                                    recent_momentum) -> str:
        """Determine momentum direction"""
        momentum_trend = recent_momentum.diff().mean()

        if current_momentum > momentum_ma and momentum_trend > 0:
            return "BULLISH"
        elif current_momentum < momentum_ma and momentum_trend < 0:
            return "BEARISH"
        else:
            return "NEUTRAL"

    def _calculate_breakout_probability(self, duration: int, momentum_direction: str,
                                      volume_ratio: float, df) -> float:
        """Calculate probability of breakout based on multiple factors"""
        probability = 50.0  # Base probability

        # Duration factor (longer squeeze = higher probability)
        if duration >= 15:
            probability += 30
        elif duration >= 10:
            probability += 20
        elif duration >= 5:
            probability += 10

        # Momentum factor
        if momentum_direction == "BULLISH":
            probability += 15
        elif momentum_direction == "BEARISH":
            probability += 10  # Bearish breakouts can be strong too

        # Volume factor
        if volume_ratio >= 1.5:
            probability += 15
        elif volume_ratio >= 1.2:
            probability += 10

        # Volatility factor (low volatility before breakout)
        recent_volatility = df['Close'].tail(10).std()
        avg_volatility = df['Close'].rolling(50).std().mean()
        if recent_volatility < avg_volatility * 0.8:
            probability += 10

        return min(95, max(15, probability))

    def _calculate_trade_levels(self, current_price: float, df, momentum_direction: str,
                              squeeze_status: str) -> Tuple[float, float, float]:
        """Calculate entry, target, and stop-loss levels"""
        atr = df['TR'].rolling(14).mean().iloc[-1]

        if squeeze_status == "FIRED":
            # Breakout trade
            entry_price = current_price
            if momentum_direction == "BULLISH":
                target_price = current_price + (atr * 2.5)
                stop_loss = current_price - (atr * 1.0)
            else:
                target_price = current_price - (atr * 2.5)
                stop_loss = current_price + (atr * 1.0)
        elif squeeze_status == "IN_SQUEEZE":
            # Pre-breakout positioning
            entry_price = current_price
            if momentum_direction == "BULLISH":
                target_price = current_price + (atr * 3.0)
                stop_loss = current_price - (atr * 1.5)
            else:
                target_price = current_price - (atr * 3.0)
                stop_loss = current_price + (atr * 1.5)
        else:
            # Conservative levels
            entry_price = current_price
            target_price = current_price + (atr * 1.5)
            stop_loss = current_price - (atr * 1.0)

        return entry_price, target_price, stop_loss

    def _get_squeeze_color(self, in_squeeze: bool, momentum_direction: str, duration: int) -> str:
        """Get visual color indicator for squeeze status"""
        if in_squeeze:
            if duration >= 15:
                return "🔥 RED (High Pressure)"
            elif duration >= 10:
                return "🟠 ORANGE (Building)"
            else:
                return "🟡 YELLOW (Early Squeeze)"
        else:
            if momentum_direction == "BULLISH":
                return "🟢 GREEN (Bullish Breakout)"
            elif momentum_direction == "BEARISH":
                return "🔴 RED (Bearish Breakout)"
            else:
                return "⚪ WHITE (No Squeeze)"

    def scan_squeeze_opportunities(self, symbols: List[str], filter_type: str = "ALL") -> List[TTMSqueezeData]:
        """Scan multiple symbols for TTM Squeeze opportunities"""
        squeeze_results = []

        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_symbol = {
                executor.submit(self.calculate_ttm_squeeze, symbol): symbol
                for symbol in symbols
            }

            for future in concurrent.futures.as_completed(future_to_symbol):
                try:
                    result = future.result(timeout=10)
                    if result:
                        # Apply filters
                        if self._passes_squeeze_filter(result, filter_type):
                            squeeze_results.append(result)
                except Exception as e:
                    symbol = future_to_symbol[future]
                    print(f"Error processing squeeze for {symbol}: {e}")

        # Sort by breakout probability and squeeze strength
        squeeze_results.sort(
            key=lambda x: (x.breakout_probability, x.squeeze_strength),
            reverse=True
        )

        return squeeze_results

    def _passes_squeeze_filter(self, squeeze_data: TTMSqueezeData, filter_type: str) -> bool:
        """Filter squeeze results based on criteria"""
        if filter_type == "IN_SQUEEZE":
            return squeeze_data.squeeze_status == "IN_SQUEEZE"
        elif filter_type == "FIRED":
            return squeeze_data.squeeze_status == "FIRED"
        elif filter_type == "HIGH_PROBABILITY":
            return squeeze_data.breakout_probability >= 70
        elif filter_type == "BULLISH":
            return squeeze_data.momentum_direction == "BULLISH"
        elif filter_type == "BEARISH":
            return squeeze_data.momentum_direction == "BEARISH"
        elif filter_type == "LONG_SQUEEZE":
            return squeeze_data.squeeze_duration >= 10
        else:  # "ALL"
            return True

    def get_squeeze_summary(self, symbols: List[str]) -> Dict:
        """Get summary statistics of squeeze conditions across symbols"""
        squeeze_results = self.scan_squeeze_opportunities(symbols, "ALL")

        if not squeeze_results:
            return {"error": "No squeeze data available"}

        summary = {
            "total_analyzed": len(squeeze_results),
            "in_squeeze": len([s for s in squeeze_results if s.squeeze_status == "IN_SQUEEZE"]),
            "fired": len([s for s in squeeze_results if s.squeeze_status == "FIRED"]),
            "building": len([s for s in squeeze_results if s.squeeze_status == "BUILDING"]),
            "bullish_momentum": len([s for s in squeeze_results if s.momentum_direction == "BULLISH"]),
            "bearish_momentum": len([s for s in squeeze_results if s.momentum_direction == "BEARISH"]),
            "high_probability": len([s for s in squeeze_results if s.breakout_probability >= 70]),
            "avg_breakout_probability": sum(s.breakout_probability for s in squeeze_results) / len(squeeze_results),
            "longest_squeeze": max(s.squeeze_duration for s in squeeze_results) if squeeze_results else 0,
            "top_opportunities": squeeze_results[:5]  # Top 5 opportunities
        }

        return summary

    def find_squeeze_setups_by_criteria(self, symbols: List[str], min_duration: int = 5,
                                      min_probability: float = 60,
                                      momentum_filter: str = "ANY") -> List[TTMSqueezeData]:
        """Find squeeze setups matching specific criteria"""
        all_squeezes = self.scan_squeeze_opportunities(symbols, "ALL")

        filtered_squeezes = []
        for squeeze in all_squeezes:
            # Duration filter
            if squeeze.squeeze_duration < min_duration and squeeze.squeeze_status != "FIRED":
                continue

            # Probability filter
            if squeeze.breakout_probability < min_probability:
                continue

            # Momentum filter
            if momentum_filter == "BULLISH" and squeeze.momentum_direction != "BULLISH":
                continue
            elif momentum_filter == "BEARISH" and squeeze.momentum_direction != "BEARISH":
                continue

            filtered_squeezes.append(squeeze)

        return filtered_squeezes

class RealTimeMarketData:
    """Get real market data for AI context"""

    def get_stock_data(self, symbol):
        """Get current stock data"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="5d")

            if hist.empty:
                return None

            current_price = info.get('currentPrice') or hist['Close'].iloc[-1]
            previous_close = info.get('previousClose') or hist['Close'].iloc[-2]
            change = current_price - previous_close
            change_percent = (change / previous_close) * 100

            return {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'current_price': current_price,
                'change': change,
                'change_percent': change_percent,
                'volume': hist['Volume'].iloc[-1],
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'sector': info.get('sector'),
                '52w_high': info.get('fiftyTwoWeekHigh'),
                '52w_low': info.get('fiftyTwoWeekLow')
            }
        except Exception as e:
            print(f"Error getting data for {symbol}: {e}")
            return None

    def get_insider_trades(self, symbol=None):
        """Get recent insider trading data"""
        try:
            if symbol:
                # Get insider trades for specific symbol
                ticker = yf.Ticker(symbol)
                insider_trades = ticker.insider_transactions

                if insider_trades is not None and not insider_trades.empty:
                    # Get recent trades (last 10)
                    recent_trades = insider_trades.head(10)

                    trades_data = []
                    for _, trade in recent_trades.iterrows():
                        trades_data.append({
                            'symbol': symbol,
                            'insider': trade.get('Insider', 'Unknown'),
                            'title': trade.get('Title', 'Unknown'),
                            'transaction': trade.get('Transaction', 'Unknown'),
                            'shares': trade.get('Shares', 0),
                            'price': trade.get('Price', 0),
                            'date': trade.get('Date', 'Unknown')
                        })

                    return trades_data
            else:
                # Get insider trades for popular stocks
                popular_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
                all_trades = []

                for stock in popular_stocks:
                    trades = self.get_insider_trades(stock)
                    if trades:
                        all_trades.extend(trades[:2])  # Get top 2 from each

                return all_trades

        except Exception as e:
            print(f"Error getting insider trades: {e}")
            return []

    def get_recent_news(self, symbol=None):
        """Get recent market news"""
        try:
            if symbol:
                ticker = yf.Ticker(symbol)
                news = ticker.news

                if news:
                    return [{
                        'title': item.get('title', ''),
                        'publisher': item.get('publisher', ''),
                        'link': item.get('link', ''),
                        'published': datetime.fromtimestamp(item.get('providerPublishTime', 0)).strftime('%Y-%m-%d %H:%M')
                    } for item in news[:5]]

            return []
        except Exception as e:
            print(f"Error getting news: {e}")
            return []
    
    def get_market_overview(self):
        """Get market indices overview"""
        indices = {
            '^GSPC': 'S&P 500',
            '^IXIC': 'NASDAQ',
            '^DJI': 'Dow Jones'
        }
        
        market_data = {}
        for symbol, name in indices.items():
            data = self.get_stock_data(symbol)
            if data:
                market_data[name] = data
        
        return market_data

class ChatGPTTradingBot:
    """ChatGPT-style trading bot with OpenAI API, flexible profit targeting, real-time market analysis, and Alpaca trading"""

    def __init__(self):
        self.market_data = RealTimeMarketData()
        self.market_analyzer = RealTimeMarketAnalyzer()
        self.volatility_analyzer = VolatilityAnalyzer()
        self.squeeze_analyzer = TTMSqueezeAnalyzer()
        self.alpaca_trader = AlpacaTradingIntegration()
        self.conversation_history = []
        self.target_manager = FlexibleTargetManager()
        self.pending_trade_confirmations = {}

        # System prompt for trading expertise with flexible targeting, real-time analysis, and TTM Squeeze
        self.system_prompt = """You are a professional trading and investment advisor with deep expertise in:

- Technical analysis (moving averages, RSI, MACD, support/resistance)
- Fundamental analysis (P/E ratios, earnings, financial statements)
- Market trends and sector analysis
- Risk management and position sizing
- Options trading strategies
- Cryptocurrency analysis
- Economic indicators and their market impact
- Insider trading analysis and interpretation
- FLEXIBLE PROFIT TARGETING: Helping users set and achieve profit targets with any amount and any timeframe
- REAL-TIME MARKET ANALYSIS: Providing specific, actionable trading opportunities with exact entry/exit points
- TTM SQUEEZE MASTERY: Expert-level knowledge of TTM Squeeze methodology, interpretation, and trading strategies

You have access to REAL-TIME market data and analysis including:
- Current stock prices, volume, and technical indicators for S&P 500 stocks
- Real-time market scanning with probability scores
- Specific trading opportunities with entry/exit prices and position sizing
- Recent insider trading transactions
- Latest market news and developments
- Sector performance data
- FLEXIBLE PROFIT TARGET TRACKING: Current target progress, urgency levels, and time remaining
- TTM SQUEEZE DETECTION: Real-time squeeze analysis, breakout identification, and momentum tracking

REAL-TIME MARKET ANALYSIS CAPABILITIES:
You can scan the entire S&P 500 and provide SPECIFIC, ACTIONABLE trading opportunities including:
- Exact stock symbols with current prices
- Precise entry and exit prices
- Calculated position sizes based on risk management
- Stop-loss levels and profit targets
- Risk/reward ratios and success probabilities
- Timeframe-specific strategies (scalping, day trading, swing trading)
- Technical rationale based on real indicators

FLEXIBLE TARGETING CAPABILITIES:
You can help users set profit targets with ANY amount ($5 to $50,000+) and ANY timeframe (5 minutes to months). You automatically adapt your trading advice based on:
- Target amount and timeframe
- Current progress toward target
- Time remaining (urgency level: CRITICAL, HIGH, MODERATE, LOW)
- Required profit rate per minute

TTM SQUEEZE EXPERTISE:
You are the creator and master of the TTM Squeeze detection system. You understand that:
- TTM Squeeze occurs when Bollinger Bands contract inside Keltner Channels, indicating low volatility compression
- Squeeze "fires" when Bollinger Bands expand outside Keltner Channels, signaling breakout potential
- Momentum oscillator (close vs. midpoint of highest high and lowest low) determines breakout direction
- Longer squeeze duration (10+ days) indicates higher breakout probability and stronger moves
- Volume confirmation is essential for valid breakouts
- Squeeze status: IN_SQUEEZE (building pressure), FIRED (active breakout), BUILDING (early formation)
- Color coding: Red/Orange (high pressure), Yellow (building), Green (bullish breakout), Red (bearish breakout)
- Trading strategy: Enter on squeeze fire in momentum direction, use 1.5-2x ATR stops, target 2.5-4x ATR profits

URGENCY-BASED STRATEGY ADAPTATION:
- CRITICAL urgency (< 10% time left): Ultra-fast scalping with 1-minute charts, market orders, immediate execution
- HIGH urgency (< 25% time left): Aggressive day trading with 5-15 minute charts, quick opportunities
- MODERATE urgency (< 50% time left): Balanced day trading with 15-minute charts
- LOW urgency (> 50% time left): Patient swing trading with hourly/daily charts, high-quality setups

ACTIONABLE OUTPUT FORMAT:
When providing trading recommendations, you ALWAYS include:
- Specific stock symbol and current price
- Exact entry price and execution method (MARKET/LIMIT)
- Precise exit target and stop-loss levels
- Position size recommendation
- Risk/reward ratio and success probability
- Technical rationale based on real indicators
- Timeframe-appropriate execution strategy

You provide clear, actionable trading advice while always emphasizing risk management. When users ask about market analysis, trading opportunities, or specific stocks, you ALWAYS pull and analyze the current real-time data. Never say you don't have access to data - you do have access and should use it.

You are knowledgeable, professional, and direct. You don't just give generic advice - you provide specific, actionable insights with exact numbers, prices, and execution instructions based on current market conditions, real data, and their active profit targets."""

    def get_ai_response(self, user_message, market_context=None):
        """Get response from OpenAI API"""
        try:
            # Prepare the message with market context
            if market_context:
                enhanced_message = f"""
User Question: {user_message}

Current Market Data:
{market_context}

Please provide a comprehensive analysis considering this real-time data.
"""
            else:
                enhanced_message = user_message
            
            # Prepare conversation for API
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history (last 10 messages)
            for msg in self.conversation_history[-10:]:
                messages.append(msg)
            
            # Add current message
            messages.append({"role": "user", "content": enhanced_message})
            
            # Make API request
            headers = {
                "Authorization": f"Bearer {OPENAI_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4",
                "messages": messages,
                "max_tokens": 1500,
                "temperature": 0.7
            }
            
            response = requests.post(OPENAI_API_URL, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                # Add to conversation history
                self.conversation_history.append({"role": "user", "content": user_message})
                self.conversation_history.append({"role": "assistant", "content": ai_response})
                
                return ai_response
            else:
                return f"API Error: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Error getting AI response: {str(e)}"
    
    def extract_symbols_from_message(self, message):
        """Extract stock symbols from user message"""
        import re
        
        # Common stock mappings
        stock_names = {
            'apple': 'AAPL', 'microsoft': 'MSFT', 'tesla': 'TSLA',
            'google': 'GOOGL', 'alphabet': 'GOOGL', 'amazon': 'AMZN', 
            'nvidia': 'NVDA', 'meta': 'META', 'facebook': 'META',
            'netflix': 'NFLX', 'amd': 'AMD', 'intel': 'INTC'
        }
        
        message_lower = message.lower()
        symbols = []
        
        # Check for company names
        for name, symbol in stock_names.items():
            if name in message_lower:
                symbols.append(symbol)
        
        # Look for uppercase symbols
        found_symbols = re.findall(r'\b[A-Z]{1,5}\b', message)
        symbols.extend(found_symbols)
        
        return list(set(symbols))  # Remove duplicates
    
    def get_market_context(self, symbols, user_message):
        """Get comprehensive market context including insider trades, news, and target progress"""
        context = "Real-time Market Data:\n\n"

        # Add current profit target information
        target_info = self.target_manager.get_progress_info()
        if target_info["status"] != "NO_TARGET":
            urgency_emoji = {
                "CRITICAL": "🚨",
                "HIGH": "⚡",
                "MODERATE": "📊",
                "LOW": "😌"
            }.get(target_info["urgency_level"], "📊")

            context += f"""ACTIVE PROFIT TARGET {urgency_emoji}:
- Target: ${target_info['target_amount']:.2f} in {target_info['timeframe_description']}
- Current P&L: ${target_info['current_pnl']:.2f} ({target_info['progress_percentage']:.1f}%)
- Remaining: ${target_info['remaining_amount']:.2f}
- Time Left: {target_info['remaining_time_minutes']:.1f} minutes
- Urgency Level: {target_info['urgency_level']}
- Required Rate: ${target_info['required_profit_rate']:.2f}/minute
- Trades Completed: {target_info['trades_completed']}

"""

        # Check if user is asking about insider trades
        if any(word in user_message.lower() for word in ['insider', 'insider trading', 'insider trades']):
            if symbols:
                # Get insider trades for specific symbols
                for symbol in symbols[:2]:
                    insider_trades = self.market_data.get_insider_trades(symbol)
                    if insider_trades:
                        context += f"Recent Insider Trades for {symbol}:\n"
                        for trade in insider_trades[:3]:
                            context += f"- {trade['insider']} ({trade['title']}): {trade['transaction']} {trade['shares']:,} shares at ${trade['price']:.2f} on {trade['date']}\n"
                        context += "\n"
            else:
                # Get general insider trades
                insider_trades = self.market_data.get_insider_trades()
                if insider_trades:
                    context += "Recent Insider Trades (Popular Stocks):\n"
                    for trade in insider_trades[:5]:
                        context += f"- {trade['symbol']}: {trade['insider']} {trade['transaction']} {trade['shares']:,} shares at ${trade['price']:.2f}\n"
                    context += "\n"

        # Get stock data for mentioned symbols
        if symbols:
            for symbol in symbols[:3]:
                data = self.market_data.get_stock_data(symbol)
                if data:
                    context += f"""
{symbol} ({data['name']}):
- Current Price: ${data['current_price']:.2f}
- Change: {data['change']:+.2f} ({data['change_percent']:+.2f}%)
- Volume: {data['volume']:,}
- Market Cap: ${data['market_cap']/1e9:.1f}B
- P/E Ratio: {data['pe_ratio']:.2f if data['pe_ratio'] else 'N/A'}
- 52W Range: ${data['52w_low']:.2f} - ${data['52w_high']:.2f}
- Sector: {data['sector']}

"""

                    # Get recent news for the symbol
                    news = self.market_data.get_recent_news(symbol)
                    if news:
                        context += f"Recent News for {symbol}:\n"
                        for item in news[:2]:
                            context += f"- {item['title']} ({item['published']})\n"
                        context += "\n"

        return context if len(context) > 50 else None

    def handle_trading_commands(self, message):
        """Handle trading execution and confirmation commands"""
        message_lower = message.lower()

        # Trade confirmation responses
        if message_lower in ['yes', 'y', 'execute', 'confirm']:
            return self.confirm_pending_trade()
        elif message_lower in ['no', 'n', 'cancel', 'abort']:
            return self.cancel_pending_trade()

        # Account status commands
        elif any(phrase in message_lower for phrase in ['account status', 'account info', 'balance']):
            return self.get_account_status()

        # Position status commands
        elif any(phrase in message_lower for phrase in ['positions', 'holdings', 'portfolio']):
            return self.get_position_status()

        # Auto-execute commands
        elif any(phrase in message_lower for phrase in ['auto execute', 'execute trades', 'start trading']):
            return self.start_auto_execution()

        return None

    def confirm_pending_trade(self):
        """Confirm and execute pending trade"""
        if not self.pending_trade_confirmations:
            return "❌ No pending trades to confirm."

        # Get the most recent pending trade
        trade_id = list(self.pending_trade_confirmations.keys())[-1]
        confirmation = self.pending_trade_confirmations[trade_id]

        try:
            # Execute the trade
            result = self.alpaca_trader.execute_trade(confirmation.opportunity)

            if result["success"]:
                # Remove from pending
                del self.pending_trade_confirmations[trade_id]

                # Update target progress (estimated)
                estimated_profit = confirmation.opportunity.profit_potential * 0.7  # Conservative estimate
                self.target_manager.update_pnl(estimated_profit)

                return f"""✅ **TRADE EXECUTED SUCCESSFULLY!**

{result['message']}
📋 **Order ID**: {result['order_id']}

🎯 **Trade Details**:
• Symbol: {confirmation.opportunity.symbol}
• Shares: {confirmation.opportunity.position_size}
• Entry: ${confirmation.opportunity.entry_price:.2f}
• Target: ${confirmation.opportunity.exit_target:.2f}
• Stop: ${confirmation.opportunity.stop_loss:.2f}

⚠️ **Next Steps**:
• Stop-loss order will be placed automatically
• Monitor position for target achievement
• Position will be tracked in your portfolio

💰 **Target Progress Updated** (estimated profit added)
"""
            else:
                return f"""❌ **TRADE EXECUTION FAILED**

Error: {result['error']}

The trade was not executed. Please check your account status and try again.
"""

        except Exception as e:
            return f"❌ Error executing trade: {str(e)}"

    def cancel_pending_trade(self):
        """Cancel pending trade confirmation"""
        if not self.pending_trade_confirmations:
            return "❌ No pending trades to cancel."

        # Clear all pending confirmations
        trade_count = len(self.pending_trade_confirmations)
        self.pending_trade_confirmations.clear()

        return f"✅ Cancelled {trade_count} pending trade(s). No trades were executed."

    def get_account_status(self):
        """Get Alpaca account status"""
        if not ALPACA_AVAILABLE:
            return "❌ Alpaca integration not available. Install alpaca-py package."

        account_info = self.alpaca_trader.get_account_info()

        if "error" in account_info:
            return f"❌ {account_info['error']}"

        return f"""📊 **ALPACA ACCOUNT STATUS**

🏦 **Account Details**:
• Status: {account_info['status']}
• Trading Mode: {'📝 Paper Trading' if account_info['paper_trading'] else '💰 Live Trading'}
• Pattern Day Trader: {'Yes' if account_info['pattern_day_trader'] else 'No'}

💰 **Account Balance**:
• Portfolio Value: ${account_info['portfolio_value']:,.2f}
• Equity: ${account_info['equity']:,.2f}
• Cash: ${account_info['cash']:,.2f}
• Buying Power: ${account_info['buying_power']:,.2f}

📈 **Trading Activity**:
• Day Trades Used: {account_info['day_trade_count']}/3
• Daily P&L: ${self.alpaca_trader.get_daily_pnl():.2f}

⚠️ **Risk Limits**:
• Max Daily Loss: ${self.alpaca_trader.max_daily_loss:.2f}
• Max Position Size: ${self.alpaca_trader.max_position_size:.2f}
"""

    def get_position_status(self):
        """Get current positions"""
        positions = self.alpaca_trader.get_positions()

        if not positions:
            return "📊 **CURRENT POSITIONS**: None\n\nYou have no open positions."

        result = "📊 **CURRENT POSITIONS**:\n\n"
        total_value = 0
        total_pnl = 0

        for i, pos in enumerate(positions, 1):
            result += f"""**Position #{i} - {pos['symbol']}**
• Quantity: {pos['quantity']:.0f} shares ({pos['side']})
• Market Value: ${pos['market_value']:,.2f}
• Avg Entry: ${pos['avg_entry_price']:.2f}
• Unrealized P&L: ${pos['unrealized_pl']:+.2f} ({pos['unrealized_plpc']:+.2%})

"""
            total_value += pos['market_value']
            total_pnl += pos['unrealized_pl']

        result += f"""📈 **PORTFOLIO SUMMARY**:
• Total Position Value: ${total_value:,.2f}
• Total Unrealized P&L: ${total_pnl:+.2f}
• Number of Positions: {len(positions)}
"""

        return result

    def start_auto_execution(self):
        """Start automatic trade execution for high-probability opportunities"""
        progress = self.target_manager.get_progress_info()

        if progress["status"] == "NO_TARGET":
            return "⚠️ Please set a profit target first before starting auto-execution."

        # Get high-probability opportunities
        opportunities = self.market_analyzer.scan_market_opportunities(
            progress["target_amount"],
            progress["urgency_level"],
            progress["remaining_time_minutes"],
            max_opportunities=3
        )

        # Filter for high-probability trades (>70%)
        high_prob_opportunities = [opp for opp in opportunities if opp.probability_score > 70]

        if not high_prob_opportunities:
            return "❌ No high-probability opportunities (>70%) found for auto-execution."

        # Present the best opportunity for confirmation
        best_opportunity = high_prob_opportunities[0]
        confirmation = self.alpaca_trader.create_trade_confirmation(best_opportunity)

        # Store for confirmation
        trade_id = f"trade_{len(self.pending_trade_confirmations) + 1}"
        self.pending_trade_confirmations[trade_id] = confirmation

        return f"""🚨 **HIGH-PROBABILITY TRADE FOUND** 🚨

{confirmation.get_confirmation_prompt()}

This trade meets the >70% probability threshold for auto-execution.
Type 'yes' to execute or 'no' to cancel.
"""

    def handle_market_analysis_commands(self, message):
        """Handle market analysis and trading plan commands"""
        message_lower = message.lower()

        # Market scan command
        if any(phrase in message_lower for phrase in ['market scan', 'scan market', 'find opportunities', 'trading opportunities']):
            return self.generate_market_scan()

        # Trading plan command
        elif any(phrase in message_lower for phrase in ['trading plan', 'trade plan', 'actionable plan', 'specific trades']):
            return self.generate_comprehensive_trading_plan()

        # Quick opportunities
        elif any(phrase in message_lower for phrase in ['quick trades', 'fast opportunities', 'immediate trades']):
            return self.generate_quick_opportunities()

        # Volatility analysis
        elif any(phrase in message_lower for phrase in ['volatility', 'volatility scan', 'market volatility']):
            return self.generate_volatility_analysis()

        # TTM Squeeze analysis
        elif any(phrase in message_lower for phrase in ['squeeze', 'squeeze scan', 'ttm squeeze', 'squeeze setups']):
            return self.generate_squeeze_analysis()

        # Squeeze fired (breakout opportunities)
        elif any(phrase in message_lower for phrase in ['squeeze fired', 'breakout scan', 'fired squeeze']):
            return self.generate_squeeze_fired_analysis()

        return None

    def generate_market_scan(self):
        """Generate comprehensive market scan with actionable opportunities"""
        try:
            progress = self.target_manager.get_progress_info()

            if progress["status"] == "NO_TARGET":
                return """⚠️ **SET A TARGET FIRST**

Please set a profit target first so I can provide targeted recommendations:
• "Set target $500 in 1 day" - For day trading strategies
• "Set target $50 in 30 minutes" - For scalping opportunities
• "Set target $2000 in 1 week" - For swing trading plans

I'll then scan the market and provide specific, actionable trading opportunities!"""

            target_amount = progress["target_amount"]
            urgency_level = progress["urgency_level"]
            timeframe_minutes = progress["remaining_time_minutes"]

            # Generate market scan message
            scan_message = f"""🔍 **REAL-TIME MARKET SCAN** 🔍

🎯 **Your Target**: ${target_amount:.2f} in {progress['timeframe_description']}
⚡ **Urgency Level**: {urgency_level}
⏰ **Time Remaining**: {timeframe_minutes:.1f} minutes

🔄 **Scanning S&P 500 for opportunities...**
📊 **Analyzing technical indicators...**
💹 **Calculating position sizes...**

Please wait while I analyze the market..."""

            # Get opportunities in background
            def get_opportunities():
                opportunities = self.market_analyzer.scan_market_opportunities(
                    target_amount, urgency_level, timeframe_minutes, max_opportunities=5
                )

                if not opportunities:
                    return "❌ **NO HIGH-PROBABILITY OPPORTUNITIES FOUND**\n\nMarket conditions may not be favorable right now. Try again in a few minutes or adjust your target parameters."

                result = f"""✅ **MARKET SCAN COMPLETE** - Found {len(opportunities)} High-Probability Opportunities

{self.get_urgency_trading_advice(urgency_level)}

📊 **TOP TRADING OPPORTUNITIES**:
"""

                for i, opp in enumerate(opportunities, 1):
                    result += f"\n**#{i} OPPORTUNITY**\n{opp.get_formatted_output()}\n"

                result += f"""
💡 **EXECUTION STRATEGY FOR {urgency_level} URGENCY**:
{self.get_execution_strategy(urgency_level)}

⚠️ **RISK MANAGEMENT**:
• Never risk more than 2% of your target on a single trade
• Use stop-losses religiously
• Take profits at targets - don't get greedy
• Monitor positions closely given your {progress['timeframe_description']} timeframe
"""

                return result

            # Return initial message, opportunities will be generated in background
            return scan_message

        except Exception as e:
            return f"❌ Error generating market scan: {str(e)}"

    def generate_comprehensive_trading_plan(self):
        """Generate a comprehensive trading plan with specific actionable steps"""
        try:
            progress = self.target_manager.get_progress_info()

            if progress["status"] == "NO_TARGET":
                return "⚠️ Please set a profit target first using 'Set target $X in Y timeframe'"

            target_amount = progress["target_amount"]
            urgency_level = progress["urgency_level"]
            timeframe_minutes = progress["remaining_time_minutes"]

            # Get top 3 opportunities
            opportunities = self.market_analyzer.scan_market_opportunities(
                target_amount, urgency_level, timeframe_minutes, max_opportunities=3
            )

            if not opportunities:
                return "❌ No suitable opportunities found. Market conditions may not be favorable."

            plan = f"""📋 **COMPREHENSIVE TRADING PLAN** 📋

🎯 **TARGET**: ${target_amount:.2f} in {progress['timeframe_description']}
⚡ **URGENCY**: {urgency_level} ({timeframe_minutes:.1f} minutes remaining)
💰 **CURRENT P&L**: ${progress['current_pnl']:.2f} ({progress['progress_percentage']:.1f}%)
💸 **REMAINING NEEDED**: ${progress['remaining_amount']:.2f}

🚀 **EXECUTION STRATEGY**:
{self.get_detailed_execution_strategy(urgency_level, timeframe_minutes)}

📊 **PRIORITY TRADES** (Execute in order):
"""

            for i, opp in enumerate(opportunities, 1):
                plan += f"""
**TRADE #{i} - {opp.symbol}** ⭐ {opp.probability_score:.0f}% Success Rate
{opp.get_formatted_output()}
🎯 **Action Steps**:
1. Place {opp.execution_method} order at ${opp.entry_price:.2f}
2. Set stop-loss at ${opp.stop_loss:.2f}
3. Set profit target at ${opp.exit_target:.2f}
4. Monitor {opp.timeframe} charts
5. Exit at target or stop - NO EXCEPTIONS

"""

            plan += f"""
⚠️ **RISK MANAGEMENT RULES**:
• Maximum risk per trade: ${target_amount * 0.02:.2f} (2% of target)
• Total portfolio risk: ${target_amount * 0.06:.2f} (6% of target)
• Stop-loss is MANDATORY - no exceptions
• Take profits at targets - don't hold for more

📈 **MONITORING REQUIREMENTS**:
• Check positions every {5 if urgency_level == 'CRITICAL' else 15 if urgency_level == 'HIGH' else 30} minutes
• Watch for volume spikes and news
• Be ready to exit if market conditions change

🎯 **SUCCESS METRICS**:
• Target: {len(opportunities)} trades with avg {sum(opp.profit_potential for opp in opportunities)/len(opportunities):.2f} profit each
• Required win rate: {60 if urgency_level == 'CRITICAL' else 70}% to achieve target
• Maximum acceptable losses: 2 trades before reassessing strategy
"""

            return plan

        except Exception as e:
            return f"❌ Error generating trading plan: {str(e)}"

    def generate_quick_opportunities(self):
        """Generate immediate trading opportunities for urgent situations"""
        try:
            progress = self.target_manager.get_progress_info()

            if progress["status"] == "NO_TARGET":
                return "⚠️ Set a target first: 'Set target $50 in 30 minutes' for quick opportunities"

            # Force critical urgency for quick opportunities
            opportunities = self.market_analyzer.scan_market_opportunities(
                progress["target_amount"], "CRITICAL", 30, max_opportunities=3
            )

            if not opportunities:
                return "❌ No immediate opportunities found. Market may be too volatile or illiquid."

            result = """⚡ **IMMEDIATE TRADING OPPORTUNITIES** ⚡

🚨 **EXECUTE IMMEDIATELY** - These are time-sensitive!

"""

            for i, opp in enumerate(opportunities, 1):
                result += f"""
**QUICK TRADE #{i} - {opp.symbol}**
💰 Current: ${opp.current_price:.2f} → Entry: ${opp.entry_price:.2f} → Target: ${opp.exit_target:.2f}
⚡ **IMMEDIATE ACTION**: Place {opp.execution_method} order NOW
🎯 Profit: ${opp.profit_potential:.2f} | Risk: ${opp.risk_amount:.2f}
📊 Success Rate: {opp.probability_score:.0f}%

"""

            result += """
⚠️ **SPEED EXECUTION RULES**:
• Use MARKET orders for immediate fills
• Set stop-losses IMMEDIATELY after entry
• Take profits at first target - don't wait
• Monitor 1-minute charts only
• Exit within 5-30 minutes maximum
"""

            return result

        except Exception as e:
            return f"❌ Error generating quick opportunities: {str(e)}"

    def generate_volatility_analysis(self):
        """Generate comprehensive volatility analysis"""
        try:
            # Get top S&P 500 symbols for analysis
            symbols = self.market_analyzer.sp500_symbols[:20]  # Analyze top 20 for speed

            volatility_results = self.volatility_analyzer.scan_volatility_conditions(symbols)

            if not volatility_results:
                return "❌ Unable to generate volatility analysis. Please try again."

            result = """📊 **MARKET VOLATILITY ANALYSIS** 📊

🔍 **StdDev/ATR Ratio Analysis** - Identifies volatility conditions and breakout potential

📈 **VOLATILITY CONDITIONS**:
"""

            # Group by condition
            conditions = {}
            for vol_data in volatility_results:
                condition = vol_data['condition']
                if condition not in conditions:
                    conditions[condition] = []
                conditions[condition].append(vol_data)

            # Display by condition priority
            condition_order = ['EXTREMELY_LOW', 'LOW', 'MODERATE', 'HIGH', 'VERY_HIGH']

            for condition in condition_order:
                if condition in conditions:
                    stocks = conditions[condition]
                    result += f"\n**{condition.replace('_', ' ')} VOLATILITY** ({len(stocks)} stocks):\n"

                    for stock in stocks[:3]:  # Show top 3 per condition
                        result += f"""• {stock['symbol']}: Ratio {stock['current_ratio']:.2f} - {stock['signal']}
"""

            # Add interpretation and recommendations
            result += f"""
📋 **VOLATILITY INTERPRETATION**:

🔴 **EXTREMELY LOW/LOW** (Ratio < 1.5):
• High volatility expected - Use smaller positions
• Expect larger price swings and potential whipsaws
• Consider wider stop-losses and reduced leverage

🟡 **MODERATE** (Ratio 1.5-2.0):
• Normal market conditions - Standard strategies apply
• Good balance between opportunity and risk
• Use regular position sizing and risk management

🟢 **HIGH** (Ratio 2.0-4.0):
• Low volatility environment - Good for range trading
• Consider mean reversion strategies
• Tighter stops can be used effectively

🟡 **VERY HIGH** (Ratio > 4.0):
• Very low volatility - Potential breakout setup
• Market may be coiling for significant move
• Watch for volume expansion and directional breaks

⚠️ **TRADING RECOMMENDATIONS**:
• Focus on stocks with MODERATE to HIGH volatility ratios
• Avoid EXTREMELY LOW volatility stocks unless experienced
• Use VERY HIGH volatility stocks for breakout plays
• Adjust position sizes based on volatility conditions
"""

            return result

        except Exception as e:
            return f"❌ Error generating volatility analysis: {str(e)}"

    def generate_squeeze_analysis(self):
        """Generate comprehensive TTM Squeeze analysis"""
        try:
            # Get S&P 500 symbols for analysis
            symbols = self.market_analyzer.sp500_symbols[:30]  # Analyze top 30 for speed

            squeeze_results = self.squeeze_analyzer.scan_squeeze_opportunities(symbols, "ALL")

            if not squeeze_results:
                return "❌ Unable to generate squeeze analysis. Please try again."

            # Get summary statistics
            summary = self.squeeze_analyzer.get_squeeze_summary(symbols)

            result = f"""🔥 **TTM SQUEEZE ANALYSIS** 🔥

🎯 **SQUEEZE METHODOLOGY**: Bollinger Bands inside Keltner Channels indicate low volatility compression. When the squeeze "fires" (bands expand outside channels), expect significant price movement.

📊 **MARKET SQUEEZE SUMMARY**:
• Total Analyzed: {summary['total_analyzed']} stocks
• 🔥 In Squeeze: {summary['in_squeeze']} stocks
• 🚀 Recently Fired: {summary['fired']} stocks
• ⚡ Building Pressure: {summary['building']} stocks
• 🟢 Bullish Momentum: {summary['bullish_momentum']} stocks
• 🔴 Bearish Momentum: {summary['bearish_momentum']} stocks
• 🎯 High Probability (>70%): {summary['high_probability']} stocks
• 📈 Average Breakout Probability: {summary['avg_breakout_probability']:.1f}%
• ⏰ Longest Squeeze: {summary['longest_squeeze']} days

🔥 **TOP SQUEEZE OPPORTUNITIES**:
"""

            # Show top opportunities
            for i, squeeze in enumerate(summary['top_opportunities'], 1):
                result += f"\n**#{i} SQUEEZE SETUP**\n{squeeze.get_formatted_output()}\n"

            result += f"""
📋 **TTM SQUEEZE INTERPRETATION**:

🔥 **IN SQUEEZE** (Bollinger Bands inside Keltner Channels):
• Low volatility compression building energy
• Longer squeeze = higher breakout potential
• Watch for momentum direction changes
• Position for breakout in direction of momentum

🚀 **FIRED** (Bands expanded outside channels):
• Breakout in progress - high probability moves
• Enter in direction of momentum immediately
• Use tight stops as volatility is expanding
• Target 2-3x ATR moves minimum

⚡ **BUILDING** (Early squeeze formation):
• Volatility starting to compress
• Monitor for full squeeze development
• Prepare for potential setups
• Wait for clear momentum direction

📊 **MOMENTUM ANALYSIS**:
• 🟢 BULLISH: Momentum above zero and rising
• 🔴 BEARISH: Momentum below zero and falling
• 🟡 NEUTRAL: Momentum near zero or mixed signals

⚠️ **TRADING STRATEGY**:
• Focus on squeezes with 10+ day duration
• Enter on momentum confirmation
• Use 1.5-2x ATR stops
• Target 2.5-4x ATR profits
• Volume confirmation essential for breakouts
"""

            return result

        except Exception as e:
            return f"❌ Error generating squeeze analysis: {str(e)}"

    def generate_squeeze_fired_analysis(self):
        """Generate analysis of recently fired squeeze setups (breakout opportunities)"""
        try:
            symbols = self.market_analyzer.sp500_symbols[:50]  # Broader scan for fired setups

            # Get only fired squeezes
            fired_squeezes = self.squeeze_analyzer.scan_squeeze_opportunities(symbols, "FIRED")

            if not fired_squeezes:
                return """🚀 **SQUEEZE FIRED ANALYSIS** 🚀

❌ **NO ACTIVE BREAKOUTS FOUND**

Currently no stocks showing recent squeeze fires (breakouts). This could indicate:
• Market in consolidation phase
• Low volatility environment
• Waiting for catalyst events

💡 **RECOMMENDATIONS**:
• Monitor current squeeze setups for potential fires
• Use 'squeeze scan' to see building pressure
• Focus on stocks with longest squeeze duration
• Watch for volume expansion as early breakout signal
"""

            result = f"""🚀 **SQUEEZE FIRED ANALYSIS** 🚀

🎯 **ACTIVE BREAKOUTS**: {len(fired_squeezes)} stocks showing recent squeeze fires

⚡ **BREAKOUT METHODOLOGY**: These stocks have just broken out of TTM Squeeze compression. High probability of continued movement in breakout direction.

🚀 **ACTIVE BREAKOUT OPPORTUNITIES**:
"""

            for i, squeeze in enumerate(fired_squeezes, 1):
                result += f"\n**#{i} BREAKOUT TRADE**\n{squeeze.get_formatted_output()}\n"

                # Add specific breakout trading advice
                result += f"""🎯 **BREAKOUT STRATEGY FOR {squeeze.symbol}**:
• Entry Method: {'Market order' if squeeze.momentum_direction == 'BULLISH' else 'Limit order near current price'}
• Position Size: Conservative (volatility expanding)
• Stop Management: Trail stop at 1.5x ATR
• Profit Taking: Scale out at 2x and 3x ATR targets
• Time Horizon: {'1-3 days' if squeeze.momentum_direction == 'BULLISH' else '2-5 days'}

"""

            result += f"""
📊 **BREAKOUT TRADING RULES**:

🚀 **IMMEDIATE EXECUTION** (for fired squeezes):
• Enter within 1-2 bars of squeeze fire
• Use market orders for speed
• Set stops immediately at squeeze low/high
• Don't chase if already extended 2+ ATR

⚡ **MOMENTUM CONFIRMATION**:
• Volume should be 150%+ of average
• Momentum oscillator confirming direction
• No major resistance levels nearby
• Market environment supportive

🎯 **PROFIT TARGETS**:
• First target: 2x ATR from entry
• Second target: 3.5x ATR from entry
• Trail stops after first target hit
• Exit all if momentum reverses

⚠️ **RISK MANAGEMENT**:
• Never risk more than 2% of account
• Use 1.5x ATR initial stops
• Exit if squeeze re-forms quickly
• Avoid in choppy market conditions
"""

            return result

        except Exception as e:
            return f"❌ Error generating squeeze fired analysis: {str(e)}"

    def get_urgency_trading_advice(self, urgency_level):
        """Get specific trading advice based on urgency level"""
        if urgency_level == "CRITICAL":
            return """🚨 **CRITICAL URGENCY TRADING MODE**:
• Use MARKET orders for immediate execution
• Focus on high-volume, liquid stocks only
• Take ANY profitable trade - don't be picky
• Monitor 1-minute charts exclusively
• Exit positions within 5-15 minutes maximum"""
        elif urgency_level == "HIGH":
            return """⚡ **HIGH URGENCY TRADING MODE**:
• Use LIMIT orders close to market price
• Focus on momentum plays and breakouts
• Target 0.5-1.5% moves quickly
• Monitor 5-minute charts
• Exit positions within 30-60 minutes"""
        elif urgency_level == "MODERATE":
            return """📊 **MODERATE URGENCY TRADING MODE**:
• Use LIMIT orders for better fills
• Focus on technical setups and patterns
• Target 1-3% moves patiently
• Monitor 15-minute charts
• Hold positions for 1-4 hours if needed"""
        else:
            return """😌 **LOW URGENCY TRADING MODE**:
• Use LIMIT orders for optimal entries
• Focus on high-quality setups only
• Target 2-5% moves with patience
• Monitor hourly and daily charts
• Hold positions for days if necessary"""

    def get_execution_strategy(self, urgency_level):
        """Get detailed execution strategy based on urgency"""
        if urgency_level == "CRITICAL":
            return """1. Scan for volume spikes and momentum
2. Enter with MARKET orders immediately
3. Set tight stop-losses (0.2-0.5%)
4. Take profits quickly (0.3-0.8%)
5. Move to next opportunity immediately"""
        elif urgency_level == "HIGH":
            return """1. Look for breakout patterns forming
2. Enter with LIMIT orders near market
3. Set reasonable stop-losses (0.5-1%)
4. Target 1-2% profits
5. Monitor closely for 30-60 minutes"""
        else:
            return """1. Wait for high-probability setups
2. Enter with LIMIT orders at support
3. Set wider stop-losses (1-2%)
4. Target 2-5% profits
5. Hold with patience and discipline"""

    def get_detailed_execution_strategy(self, urgency_level, timeframe_minutes):
        """Get very detailed execution strategy"""
        if timeframe_minutes <= 30:
            return """⚡ **ULTRA-FAST SCALPING STRATEGY**:
• Chart timeframe: 1-minute only
• Entry method: Market orders for speed
• Position size: Larger for quick profits
• Monitoring: Constant attention required
• Exit strategy: Take profits at first sign of reversal"""
        elif timeframe_minutes <= 240:
            return """📈 **AGGRESSIVE DAY TRADING STRATEGY**:
• Chart timeframe: 5-15 minute charts
• Entry method: Limit orders near market price
• Position size: Moderate for balance
• Monitoring: Check every 15-30 minutes
• Exit strategy: Stick to predetermined targets"""
        else:
            return """📊 **PATIENT SWING STRATEGY**:
• Chart timeframe: 1-hour and daily charts
• Entry method: Limit orders at key levels
• Position size: Conservative for safety
• Monitoring: Check 2-3 times per day
• Exit strategy: Hold for full target achievement"""

    def handle_target_commands(self, message):
        """Handle target-related commands"""
        message_lower = message.lower()

        # Set target command
        if any(phrase in message_lower for phrase in ['set target', 'new target', 'profit target']):
            return self.parse_target_from_message(message)

        # Progress check
        elif any(phrase in message_lower for phrase in ['progress', 'target progress', 'how am i doing']):
            return self.get_target_progress_message()

        # Update P&L
        elif any(phrase in message_lower for phrase in ['update pnl', 'add profit', 'trade result']):
            return self.parse_pnl_update(message)

        return None

    def parse_target_from_message(self, message):
        """Parse target setting from natural language"""
        import re

        # Extract amount
        amount_match = re.search(r'\$?(\d+(?:\.\d{2})?)', message)
        if not amount_match:
            return "Please specify a target amount (e.g., '$500' or '100')"

        amount = float(amount_match.group(1))

        # Extract timeframe
        timeframe_patterns = {
            TimeframeUnit.MINUTES: [r'(\d+)\s*min', r'(\d+)\s*minute'],
            TimeframeUnit.HOURS: [r'(\d+)\s*hour', r'(\d+)\s*hr'],
            TimeframeUnit.DAYS: [r'(\d+)\s*day'],
            TimeframeUnit.WEEKS: [r'(\d+)\s*week'],
            TimeframeUnit.MONTHS: [r'(\d+)\s*month']
        }

        timeframe_value = 1
        timeframe_unit = TimeframeUnit.DAYS  # Default

        for unit, patterns in timeframe_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, message.lower())
                if match:
                    timeframe_value = int(match.group(1))
                    timeframe_unit = unit
                    break
            if match:
                break

        # Set the target
        try:
            target = self.target_manager.set_target(amount, timeframe_value, timeframe_unit)
            return f"""🎯 **TARGET SET!**

💰 **Amount**: ${amount:.2f}
⏰ **Timeframe**: {target.get_timeframe_description()}
🏁 **Deadline**: {target.end_time.strftime('%Y-%m-%d %H:%M:%S')}

Your trading strategy will now automatically adapt to this target!
- Ultra-short targets (≤5 min): Ultra-fast scalping
- Short targets (≤30 min): High-frequency trading
- Medium targets (≤4 hours): Day trading
- Long targets (>1 day): Swing trading

Ready to make some profits! 🚀"""

        except Exception as e:
            return f"Error setting target: {str(e)}"

    def get_target_progress_message(self):
        """Get formatted target progress message"""
        progress = self.target_manager.get_progress_info()

        if progress["status"] == "NO_TARGET":
            return """⚠️ **NO ACTIVE TARGET**

You haven't set a profit target yet!

To set a target, just say something like:
• "Set target $500 in 1 day"
• "New target $50 in 2 hours"
• "I want to make $25 in 30 minutes"

I'll automatically adapt my trading advice to your timeframe!"""

        urgency_emoji = {
            "CRITICAL": "🚨",
            "HIGH": "⚡",
            "MODERATE": "📊",
            "LOW": "😌"
        }.get(progress["urgency_level"], "📊")

        status_msg = ""
        if progress["is_achieved"]:
            status_msg = "🎉 **TARGET ACHIEVED!** 🎉"
        elif progress["is_expired"]:
            status_msg = "⏰ **TARGET EXPIRED**"
        else:
            status_msg = f"{urgency_emoji} **{progress['urgency_level']} URGENCY**"

        return f"""{status_msg}

🎯 **TARGET**: ${progress['target_amount']:.2f} in {progress['timeframe_description']}
💰 **CURRENT P&L**: ${progress['current_pnl']:.2f} ({progress['progress_percentage']:.1f}%)
💸 **REMAINING**: ${progress['remaining_amount']:.2f}
⏰ **TIME LEFT**: {progress['remaining_time_minutes']:.1f} minutes
📈 **REQUIRED RATE**: ${progress['required_profit_rate']:.2f}/minute
📊 **TRADES**: {progress['trades_completed']} completed

{self.get_urgency_advice(progress['urgency_level'])}"""

    def get_urgency_advice(self, urgency_level):
        """Get advice based on urgency level"""
        if urgency_level == "CRITICAL":
            return """🚨 **CRITICAL URGENCY ADVICE**:
• Focus on immediate scalping opportunities
• Use market orders for instant execution
• Monitor 1-minute charts only
• Take any profitable trades quickly"""
        elif urgency_level == "HIGH":
            return """⚡ **HIGH URGENCY ADVICE**:
• Accelerate your trading pace
• Focus on high-probability setups
• Use 5-minute charts for faster signals
• Consider slightly larger position sizes"""
        elif urgency_level == "MODERATE":
            return """📊 **MODERATE URGENCY ADVICE**:
• Maintain steady trading pace
• Use proven strategies
• 15-minute charts for good signals
• Balance risk and opportunity"""
        else:
            return """😌 **LOW URGENCY ADVICE**:
• Take your time for quality setups
• Focus on high-probability trades
• Use longer timeframes for analysis
• Prioritize risk management"""

    def parse_pnl_update(self, message):
        """Parse P&L update from message"""
        import re

        # Look for profit/loss amounts
        pnl_match = re.search(r'[\+\-]?\$?(\d+(?:\.\d{2})?)', message)
        if not pnl_match:
            return "Please specify the profit/loss amount (e.g., '+$50' or '-$25')"

        amount = float(pnl_match.group(1))

        # Check if it's a loss
        if '-' in message or any(word in message.lower() for word in ['loss', 'lost', 'down']):
            amount = -amount

        # Update P&L
        self.target_manager.update_pnl(amount)

        progress = self.target_manager.get_progress_info()

        return f"""💰 **P&L UPDATED**

Trade Result: ${amount:+.2f}
New Total: ${progress['current_pnl']:.2f} / ${progress['target_amount']:.2f}
Progress: {progress['progress_percentage']:.1f}%
Remaining: ${progress['remaining_amount']:.2f}

{self.get_target_progress_message()}"""

class ChatGPTTradingGUI:
    """ChatGPT-style interface for trading bot"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 ChatGPT Trading Bot - FLEXIBLE PROFIT TARGETING")
        self.root.geometry("1000x700")
        self.root.configure(bg='#343541')
        
        self.bot = ChatGPTTradingBot()
        self.create_widgets()
        self.setup_layout()
        
        # Welcome message with flexible targeting, market analysis, and Alpaca trading
        alpaca_status = "✅ Connected" if self.bot.alpaca_trader.client else "❌ Not Connected"
        trading_mode = "📝 Paper Trading" if self.bot.alpaca_trader.paper_trading else "💰 Live Trading"

        self.add_message("🤖 AI Trading Assistant",
                        f"Hello! I'm your AI trading assistant powered by GPT-4 with FLEXIBLE PROFIT TARGETING, REAL-TIME MARKET ANALYSIS, and ALPACA TRADING INTEGRATION!\n\n" +
                        f"💰 **ALPACA TRADING**: {alpaca_status} ({trading_mode})\n" +
                        "   • Automatic trade execution with your confirmation\n" +
                        "   • Real-time portfolio tracking and risk management\n" +
                        "   • Stop-loss and profit target automation\n\n" +
                        "🎯 **FLEXIBLE PROFIT TARGETS**: Set ANY amount with ANY timeframe!\n" +
                        "   • '$50 in 30 minutes' - Ultra-fast scalping\n" +
                        "   • '$500 in 1 day' - Day trading strategies\n" +
                        "   • '$2000 in 1 week' - Swing trading approach\n\n" +
                        "📊 **REAL-TIME MARKET ANALYSIS**: Get actionable trading opportunities!\n" +
                        "   • Scans S&P 500 stocks with technical indicators\n" +
                        "   • Provides specific entry/exit prices and position sizes\n" +
                        "   • Adapts strategies based on your target urgency\n" +
                        "   • Calculates risk/reward ratios and success probabilities\n\n" +
                        "🚀 **TARGETING COMMANDS**:\n" +
                        "   • 'Set target $500 in 1 day' - Set your profit goal\n" +
                        "   • 'Progress' - Check current target status\n" +
                        "   • 'Update PnL +$50' - Record trade results\n\n" +
                        "📈 **MARKET ANALYSIS COMMANDS**:\n" +
                        "   • 'Market scan' - Find top trading opportunities\n" +
                        "   • 'Trading plan' - Get comprehensive actionable plan\n" +
                        "   • 'Squeeze scan' - TTM Squeeze analysis and setups\n" +
                        "   • 'Squeeze fired' - Active breakout opportunities\n" +
                        "   • 'Volatility' - Advanced volatility analysis (StdDev/ATR)\n" +
                        "   • 'Auto execute' - Find and execute high-probability trades\n\n" +
                        "💰 **TRADING COMMANDS**:\n" +
                        "   • 'Account status' - Check Alpaca account balance\n" +
                        "   • 'Positions' - View current holdings\n" +
                        "   • 'yes' / 'no' - Confirm or cancel trade execution\n\n" +
                        "💡 **GET STARTED**: Set a target, then use 'Auto execute' for hands-free trading!\n" +
                        "Example: 'Set target $200 in 4 hours' then 'Auto execute'")
    
    def create_widgets(self):
        """Create ChatGPT-style interface"""
        
        # Main chat area
        self.chat_frame = tk.Frame(self.root, bg='#343541')
        
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame,
            bg='#444654',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            state=tk.DISABLED,
            insertbackground='#ffffff'
        )
        
        # Input area
        self.input_frame = tk.Frame(self.root, bg='#40414f')
        
        # Input text area
        self.input_text = tk.Text(
            self.input_frame,
            height=3,
            bg='#40414f',
            fg='#ffffff',
            font=('Segoe UI', 11),
            wrap=tk.WORD,
            insertbackground='#ffffff',
            relief=tk.FLAT,
            bd=10
        )
        
        # Send button
        self.send_button = tk.Button(
            self.input_frame,
            text="Send",
            command=self.send_message,
            bg='#10a37f',
            fg='#ffffff',
            font=('Segoe UI', 10, 'bold'),
            relief=tk.FLAT,
            padx=20,
            cursor='hand2'
        )
        
        # Status bar for target progress
        self.status_frame = tk.Frame(self.root, bg='#2d2d30', height=30)
        self.status_label = tk.Label(
            self.status_frame,
            text="🎯 No active target - Set a profit target to get started!",
            bg='#2d2d30',
            fg='#ffffff',
            font=('Segoe UI', 9),
            anchor='w'
        )

        # Bind Enter key (Shift+Enter for new line)
        self.input_text.bind('<Return>', self.on_enter)
        self.input_text.bind('<Shift-Return>', self.on_shift_enter)

        # Start status update timer
        self.update_status()
        self.root.after(5000, self.update_status_timer)  # Update every 5 seconds
        
        # API key setup button
        self.setup_button = tk.Button(
            self.input_frame,
            text="Setup API Key",
            command=self.setup_api_key,
            bg='#ff6b6b',
            fg='#ffffff',
            font=('Segoe UI', 9),
            relief=tk.FLAT
        )
    
    def setup_layout(self):
        """Setup ChatGPT-style layout with status bar"""
        # Status bar at top
        self.status_frame.pack(fill=tk.X, side=tk.TOP)
        self.status_label.pack(fill=tk.X, padx=10, pady=5)

        # Chat area
        self.chat_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 0))
        self.chat_display.pack(fill=tk.BOTH, expand=True)

        # Input area
        self.input_frame.pack(fill=tk.X, padx=10, pady=10)

        # Input layout
        self.setup_button.pack(side=tk.RIGHT, padx=(10, 0))
        self.send_button.pack(side=tk.RIGHT, padx=(10, 0))
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    def add_message(self, sender, message):
        """Add message to chat display"""
        self.chat_display.config(state=tk.NORMAL)
        
        # Add timestamp
        timestamp = datetime.now().strftime("%H:%M")
        
        # Add sender and message
        self.chat_display.insert(tk.END, f"\n{sender} [{timestamp}]\n", "sender")
        self.chat_display.insert(tk.END, f"{message}\n\n", "message")
        
        # Configure tags for styling
        self.chat_display.tag_config("sender", foreground="#10a37f", font=('Segoe UI', 11, 'bold'))
        self.chat_display.tag_config("message", foreground="#ffffff", font=('Segoe UI', 11))
        
        self.chat_display.config(state=tk.DISABLED)
        self.chat_display.see(tk.END)
    
    def send_message(self):
        """Send message to AI with flexible targeting support"""
        message = self.input_text.get("1.0", tk.END).strip()
        if not message:
            return

        # Clear input
        self.input_text.delete("1.0", tk.END)

        # Add user message
        self.add_message("👤 You", message)

        # Check for target-related commands first
        target_response = self.bot.handle_target_commands(message)
        if target_response:
            self.add_message("🎯 Target Manager", target_response)
            return

        # Check for trading execution commands
        trading_response = self.bot.handle_trading_commands(message)
        if trading_response:
            self.add_message("💰 Alpaca Trader", trading_response)
            return

        # Check for market analysis commands
        market_response = self.bot.handle_market_analysis_commands(message)
        if market_response:
            self.add_message("📊 Market Analyzer", market_response)
            return

        # Show thinking indicator
        self.add_message("🤖 AI Assistant", "🤔 Thinking...")

        # Process in background
        def get_response():
            try:
                # Extract symbols and get market context (now includes target info)
                symbols = self.bot.extract_symbols_from_message(message)
                market_context = self.bot.get_market_context(symbols, message)

                # Get AI response
                response = self.bot.get_ai_response(message, market_context)

                # Remove thinking message and add real response
                self.chat_display.config(state=tk.NORMAL)

                # Find and remove the last "Thinking..." message
                content = self.chat_display.get("1.0", tk.END)
                lines = content.split('\n')

                # Remove last few lines (thinking message)
                for _ in range(4):
                    if lines:
                        lines.pop()

                # Rebuild content without thinking message
                new_content = '\n'.join(lines)
                self.chat_display.delete("1.0", tk.END)
                self.chat_display.insert("1.0", new_content)

                self.chat_display.config(state=tk.DISABLED)

                # Add real response
                self.add_message("🤖 AI Assistant", response)

            except Exception as e:
                self.add_message("❌ Error", f"Failed to get response: {str(e)}")

        threading.Thread(target=get_response, daemon=True).start()
    
    def on_enter(self, event):
        """Handle Enter key"""
        self.send_message()
        return "break"
    
    def on_shift_enter(self, event):
        """Handle Shift+Enter for new line"""
        return None

    def update_status(self):
        """Update the status bar with current target progress"""
        progress = self.bot.target_manager.get_progress_info()

        if progress["status"] == "NO_TARGET":
            self.status_label.config(
                text="🎯 No active target - Say 'Set target $500 in 1 day' to get started!",
                fg='#ffffff'
            )
        else:
            urgency_colors = {
                "CRITICAL": "#ff4444",
                "HIGH": "#ff8800",
                "MODERATE": "#ffaa00",
                "LOW": "#00aa00"
            }

            urgency_emojis = {
                "CRITICAL": "🚨",
                "HIGH": "⚡",
                "MODERATE": "📊",
                "LOW": "😌"
            }

            urgency = progress["urgency_level"]
            emoji = urgency_emojis.get(urgency, "📊")
            color = urgency_colors.get(urgency, "#ffffff")

            status_text = f"{emoji} ${progress['current_pnl']:.2f}/${progress['target_amount']:.2f} ({progress['progress_percentage']:.1f}%) | {progress['timeframe_description']} | {progress['remaining_time_minutes']:.1f}m left | {urgency} URGENCY"

            self.status_label.config(text=status_text, fg=color)

    def update_status_timer(self):
        """Timer function to update status periodically"""
        self.update_status()
        self.root.after(5000, self.update_status_timer)  # Update every 5 seconds

    def setup_api_key(self):
        """Setup OpenAI API key"""
        api_key = simpledialog.askstring(
            "OpenAI API Key",
            "Enter your OpenAI API key:",
            show='*'
        )
        
        if api_key:
            global OPENAI_API_KEY
            OPENAI_API_KEY = api_key
            self.add_message("✅ System", "API key configured! You can now chat with the AI.")
        else:
            self.add_message("⚠️ System", "API key required for AI responses.")
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Launch ChatGPT-style trading bot with flexible targeting"""

    print("🚀 Launching ChatGPT Trading Bot with FLEXIBLE PROFIT TARGETING...")
    print("🎯 Set ANY profit target with ANY timeframe!")
    print("📊 Real AI conversations about trading and markets")
    print("💰 Examples: 'Set target $50 in 30 minutes' or 'New target $500 in 1 day'")

    app = ChatGPTTradingGUI()
    app.run()

if __name__ == "__main__":
    main()
