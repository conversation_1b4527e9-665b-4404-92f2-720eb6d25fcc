#!/usr/bin/env python3
"""
Enhanced Trading Bot Launcher
Easy launcher for the sophisticated trading system
"""

import sys
import os
import subprocess
import time

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        'yfinance', 'pandas', 'numpy', 'requests', 'python-dotenv',
        'alpaca-py', 'openai', 'ta', 'scikit-learn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Installing missing packages...")
        
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    return True

def display_banner():
    """Display patriotic banner"""
    banner = """
🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸

    ████████╗██████╗  █████╗ ██████╗ ██╗███╗   ██╗ ██████╗ 
    ╚══██╔══╝██╔══██╗██╔══██╗██╔══██╗██║████╗  ██║██╔════╝ 
       ██║   ██████╔╝███████║██║  ██║██║██╔██╗ ██║██║  ███╗
       ██║   ██╔══██╗██╔══██║██║  ██║██║██║╚██╗██║██║   ██║
       ██║   ██║  ██║██║  ██║██████╔╝██║██║ ╚████║╚██████╔╝
       ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝ ╚═╝╚═╝  ╚═══╝ ╚═════╝ 
                                                            
    ██████╗  ██████╗ ████████╗    ██╗   ██╗██████╗  ██████╗ 
    ██╔══██╗██╔═══██╗╚══██╔══╝    ██║   ██║╚════██╗██╔═████╗
    ██████╔╝██║   ██║   ██║       ██║   ██║ █████╔╝██║██╔██║
    ██╔══██╗██║   ██║   ██║       ╚██╗ ██╔╝██╔═══╝ ████╔╝██║
    ██████╔╝╚██████╔╝   ██║        ╚████╔╝ ███████╗╚██████╔╝
    ╚═════╝  ╚═════╝    ╚═╝         ╚═══╝  ╚══════╝ ╚═════╝ 

🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸

    🦅 AMERICA FIRST TRADING BOT v2.0 🦅
    
    ⭐ MAKING PORTFOLIOS GREAT AGAIN! ⭐
    
    🚀 Features:
    • Daily Profit Targets with Smart Position Sizing
    • S&P 500 + 100B+ Market Cap Stock Scanner  
    • Real-time Pattern Detection & Analysis
    • Trump-Style AI Personality for Trading Insights
    • Advanced Risk Management & Strategy Selection
    • Live Market Monitoring & Actionable Alerts
    
    💰 Ready to DOMINATE the markets? Let's GO!

🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸🇺🇸
"""
    print(banner)

def get_user_preferences():
    """Get user trading preferences"""
    print("\n🎯 TRADING CONFIGURATION")
    print("=" * 50)
    
    # Daily profit target
    while True:
        try:
            target = input("💰 Enter your daily profit target (default $500): ").strip()
            if not target:
                target = 500.0
            else:
                target = float(target)
            
            if target <= 0:
                print("❌ Profit target must be positive!")
                continue
            break
        except ValueError:
            print("❌ Please enter a valid number!")
    
    # Risk tolerance
    print("\n⚠️ Risk Tolerance:")
    print("1. Conservative (1% risk per trade)")
    print("2. Moderate (1.5% risk per trade) - RECOMMENDED")
    print("3. Aggressive (2% risk per trade)")
    
    while True:
        risk_choice = input("Select risk level (1-3, default 2): ").strip()
        if not risk_choice:
            risk_level = 0.015  # 1.5%
            break
        elif risk_choice == "1":
            risk_level = 0.01   # 1%
            break
        elif risk_choice == "2":
            risk_level = 0.015  # 1.5%
            break
        elif risk_choice == "3":
            risk_level = 0.02   # 2%
            break
        else:
            print("❌ Please select 1, 2, or 3!")
    
    # Trading style
    print("\n📈 Preferred Trading Style:")
    print("1. Conservative - Focus on high-probability setups")
    print("2. Balanced - Mix of safety and opportunity")
    print("3. Aggressive - Maximum profit potential")
    
    while True:
        style_choice = input("Select trading style (1-3, default 2): ").strip()
        if not style_choice or style_choice == "2":
            trading_style = "balanced"
            break
        elif style_choice == "1":
            trading_style = "conservative"
            break
        elif style_choice == "3":
            trading_style = "aggressive"
            break
        else:
            print("❌ Please select 1, 2, or 3!")
    
    return {
        'daily_target': target,
        'risk_level': risk_level,
        'trading_style': trading_style
    }

def check_api_keys():
    """Check if API keys are configured"""
    print("\n🔑 CHECKING API CONFIGURATION...")
    
    # Check if config file exists
    if not os.path.exists('config/settings.py'):
        print("❌ Configuration file not found!")
        return False
    
    try:
        from config.settings import settings
        
        # Check OpenAI key
        if not settings.OPENAI_API_KEY or settings.OPENAI_API_KEY == "your_openai_key_here":
            print("⚠️ OpenAI API key not configured - AI features will be limited")
        else:
            print("✅ OpenAI API key configured")
        
        # Check Alpaca keys
        if not settings.ALPACA_API_KEY or not settings.ALPACA_SECRET_KEY:
            print("⚠️ Alpaca API keys not configured - trading will be simulated")
        else:
            print("✅ Alpaca API keys configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        return False

def launch_bot(preferences):
    """Launch the enhanced trading bot"""
    print("\n🚀 LAUNCHING AMERICA FIRST TRADING BOT...")
    print("🎯 Configuration:")
    print(f"   • Daily Target: ${preferences['daily_target']:.2f}")
    print(f"   • Risk Level: {preferences['risk_level']*100:.1f}% per trade")
    print(f"   • Trading Style: {preferences['trading_style'].title()}")
    
    print("\n💪 GET READY TO MAKE SOME TREMENDOUS PROFITS!")
    print("🇺🇸 AMERICA FIRST! 🇺🇸")
    
    time.sleep(2)
    
    try:
        # Import and run the enhanced bot
        from enhanced_trading_bot import EnhancedTradingBotGUI
        
        app = EnhancedTradingBotGUI(daily_profit_target=preferences['daily_target'])
        app.run()
        
    except ImportError as e:
        print(f"❌ Error importing trading bot: {e}")
        print("🔧 Make sure all files are in the correct directory")
    except Exception as e:
        print(f"❌ Error launching bot: {e}")

def main():
    """Main launcher function"""
    
    # Clear screen
    os.system('cls' if os.name == 'nt' else 'clear')
    
    # Display banner
    display_banner()
    
    # Check dependencies
    print("📦 Checking dependencies...")
    if not check_dependencies():
        print("❌ Failed to install required packages. Please install manually.")
        input("Press Enter to exit...")
        return
    
    print("✅ All dependencies satisfied!")
    
    # Check API configuration
    if not check_api_keys():
        print("⚠️ Some API keys may not be configured, but the bot will still work!")
    
    # Get user preferences
    preferences = get_user_preferences()
    
    # Confirm launch
    print(f"\n🎯 Ready to launch with ${preferences['daily_target']:.2f} daily target!")
    confirm = input("🚀 Launch the bot? (Y/n): ").strip().lower()
    
    if confirm in ['', 'y', 'yes']:
        launch_bot(preferences)
    else:
        print("👋 Maybe next time! Remember - we're here to make TREMENDOUS profits!")

if __name__ == "__main__":
    main()
