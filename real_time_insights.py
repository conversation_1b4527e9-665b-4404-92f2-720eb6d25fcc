#!/usr/bin/env python3
"""
Real-Time Trading Insights Generator
Combines market analysis, AI personality, and actionable trading recommendations
"""

import asyncio
from datetime import datetime
from typing import Dict, List
import logging
import requests

from enhanced_trading_engine import EnhancedTradingEngine, TradingSignal
from trump_trading_personality import TrumpTradingPersonality
from advanced_market_scanner import AdvancedMarketScanner

logger = logging.getLogger(__name__)

class RealTimeInsightsGenerator:
    """Generate sophisticated, actionable trading insights with flexible targeting and Trump personality"""

    def __init__(self):
        self.trading_engine = EnhancedTradingEngine()
        self.personality = TrumpTradingPersonality()
        self.scanner = AdvancedMarketScanner()

        # OpenAI integration for enhanced analysis
        try:
            from config.settings import settings
            self.openai_api_key = settings.OPENAI_API_KEY
        except:
            self.openai_api_key = None
            logger.warning("OpenAI API key not found - AI features will be limited")

        self.openai_url = "https://api.openai.com/v1/chat/completions"

        logger.info("Real-Time Insights Generator initialized with flexible targeting")

    async def generate_flexible_trading_plan(self) -> Dict:
        """Generate comprehensive flexible trading plan based on current target"""

        logger.info("Generating flexible trading plan...")

        # Check if target is set
        progress = self.trading_engine.get_target_progress()
        if progress['status'] == 'NO_TARGET':
            return {
                'error': 'No target set',
                'formatted_output': "⚠️ Please set a profit target first using the 'SET TARGET' button!"
            }

        # Get market analysis
        market_analysis = await self.trading_engine.analyze_market_conditions()

        # Perform market scan adapted to current target urgency
        scan_results = await self.scanner.comprehensive_market_scan(max_symbols=50)

        # Get sector analysis
        sector_analysis = await self.scanner.get_sector_analysis()

        # Generate trading signals based on current target
        trading_signals = await self.trading_engine.scan_for_opportunities(market_analysis)

        # Get urgency recommendations
        urgency_recommendations = self.trading_engine.get_urgency_recommendations()

        # Create comprehensive plan
        plan = {
            'target_progress': progress,
            'market_analysis': market_analysis,
            'trading_signals': trading_signals,
            'urgency_recommendations': urgency_recommendations,
            'adaptive_params': self.trading_engine.get_adaptive_strategy_params()
        }

        # Enhance with AI insights
        ai_insights = await self._get_ai_enhanced_insights(
            market_analysis, scan_results[:10], trading_signals, progress
        )

        # Format with Trump personality
        formatted_plan = self._format_flexible_plan(
            plan, scan_results, sector_analysis, ai_insights
        )

        return {
            'raw_plan': plan,
            'scan_results': scan_results,
            'sector_analysis': sector_analysis,
            'ai_insights': ai_insights,
            'formatted_output': formatted_plan,
            'timestamp': datetime.now().isoformat()
        }

    async def generate_daily_trading_plan(self) -> Dict:
        """Generate comprehensive daily trading plan with actionable insights"""
        
        logger.info("Generating daily trading plan...")
        
        # Get market analysis
        market_analysis = await self.trading_engine.analyze_market_conditions()
        
        # Perform comprehensive market scan
        scan_results = await self.scanner.comprehensive_market_scan(max_symbols=75)
        
        # Get sector analysis
        sector_analysis = await self.scanner.get_sector_analysis()
        
        # Generate trading signals
        trading_signals = await self.trading_engine.scan_for_opportunities(market_analysis)
        
        # Create comprehensive plan
        plan = await self.trading_engine.generate_daily_plan()
        
        # Enhance with AI insights
        ai_insights = await self._get_ai_enhanced_insights(
            market_analysis, scan_results[:10], trading_signals
        )
        
        # Format with Trump personality
        formatted_plan = self._format_comprehensive_plan(
            plan, scan_results, sector_analysis, ai_insights
        )
        
        return {
            'raw_plan': plan,
            'scan_results': scan_results,
            'sector_analysis': sector_analysis,
            'ai_insights': ai_insights,
            'formatted_output': formatted_plan,
            'timestamp': datetime.now().isoformat()
        }

    async def _get_ai_enhanced_insights(self, market_analysis, top_opportunities, signals, progress=None) -> str:
        """Get AI-enhanced market insights using OpenAI"""
        
        try:
            # Prepare context for AI
            context = f"""
Market Condition: {market_analysis.condition.value}
Volatility: {market_analysis.volatility_level}
Sentiment: {market_analysis.market_sentiment}

Top Opportunities Found:
"""
            for opp in top_opportunities[:5]:
                context += f"- {opp.symbol}: {opp.recommendation} (Score: {opp.score:.2f})\n"

            context += f"\nTrading Signals Generated: {len(signals)}\n"

            if signals:
                context += "Key Signals:\n"
                for signal in signals[:3]:
                    context += f"- {signal.symbol}: {signal.action} (Confidence: {signal.confidence:.1%})\n"

            # Add flexible targeting context if available
            if progress and progress['status'] != 'NO_TARGET':
                context += f"""

FLEXIBLE TARGET CONTEXT:
- Target: ${progress['target_amount']:.2f} in {progress['timeframe_description']}
- Current Progress: {progress['progress_percentage']:.1f}%
- Time Remaining: {progress['remaining_time_minutes']:.1f} minutes
- Urgency Level: {progress['urgency_level']}
- Strategy Focus: {progress['strategy_focus']}
- Required Rate: ${progress['required_profit_rate']:.2f}/minute
"""
            
            # AI prompt
            prompt = f"""
You are a professional trading analyst. Based on the current market data below, provide:

1. Market outlook for today
2. Key opportunities to watch
3. Risk factors to consider
4. Specific actionable recommendations

Market Data:
{context}

Provide a concise but comprehensive analysis focusing on actionable insights for day trading.
"""
            
            headers = {
                "Authorization": f"Bearer {self.openai_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4",
                "messages": [
                    {"role": "system", "content": "You are a professional trading analyst providing actionable market insights."},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 800,
                "temperature": 0.7
            }
            
            response = requests.post(self.openai_url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                logger.error(f"OpenAI API error: {response.status_code}")
                return "AI analysis temporarily unavailable"
                
        except Exception as e:
            logger.error(f"Error getting AI insights: {e}")
            return "AI analysis temporarily unavailable"

    def _format_flexible_plan(self, plan: Dict, scan_results: List,
                            sector_analysis: Dict, ai_insights: str) -> str:
        """Format flexible trading plan with Trump personality"""

        progress = plan['target_progress']
        urgency = progress['urgency_level']

        # Start with urgency-appropriate welcome
        if urgency == "CRITICAL":
            output = """
🚨🚨🚨 **CRITICAL URGENCY BATTLE PLAN** 🚨🚨🚨

Time is running OUT! We need to move FAST and SMART!
Every second counts - this is CRUNCH TIME!

"""
        elif urgency == "HIGH":
            output = """
⚡⚡⚡ **HIGH URGENCY ACTION PLAN** ⚡⚡⚡

We're in HIGH GEAR mode! Time to accelerate and make some QUICK profits!
The clock is ticking - let's DOMINATE!

"""
        else:
            output = self.personality.generate_welcome_message() + "\n\n"

        # Add target status
        output += self._format_target_status(progress) + "\n\n"

        # Add market analysis with urgency context
        output += self.personality.format_market_analysis(plan['market_analysis'].__dict__) + "\n\n"

        # Add urgency recommendations
        if plan['urgency_recommendations']:
            output += self._format_urgency_recommendations(plan['urgency_recommendations']) + "\n\n"

        # Add top opportunities
        if scan_results:
            output += self._format_top_opportunities(scan_results[:10]) + "\n\n"

        # Add sector analysis
        output += self._format_sector_analysis(sector_analysis) + "\n\n"

        # Add AI insights
        output += f"""
🤖 **ADVANCED AI MARKET ANALYSIS** 🤖

{ai_insights}

""" + "\n\n"

        # Add specific trading signals
        if plan.get('trading_signals'):
            output += self._format_trading_signals(plan['trading_signals']) + "\n\n"

        # Add adaptive strategy info
        output += self._format_adaptive_strategy(plan['adaptive_params']) + "\n\n"

        # Add motivational close based on urgency
        if urgency == "CRITICAL":
            output += "🚨 **CRITICAL MODE ACTIVATED!** Every trade counts! EXECUTE NOW! 🚨"
        elif urgency == "HIGH":
            output += "⚡ **HIGH SPEED TRADING!** Time to make FAST money! ⚡"
        else:
            output += self.personality.get_motivational_message()

        return output

    def _format_target_status(self, progress: Dict) -> str:
        """Format current target status"""

        urgency_emoji = {
            "CRITICAL": "🚨",
            "HIGH": "⚡",
            "MODERATE": "📊",
            "LOW": "😌"
        }.get(progress['urgency_level'], "📊")

        return f"""
{urgency_emoji} **FLEXIBLE TARGET STATUS** {urgency_emoji}

🎯 **Target**: ${progress['target_amount']:.2f} in {progress['timeframe_description']}
💰 **Current P&L**: ${progress['current_pnl']:.2f} ({progress['progress_percentage']:.1f}%)
💸 **Remaining**: ${progress['remaining_amount']:.2f}
⏰ **Time Left**: {progress['remaining_time_minutes']:.1f} minutes
📈 **Required Rate**: ${progress['required_profit_rate']:.2f}/minute
🎪 **Strategy Focus**: {progress['strategy_focus']}
"""

    def _format_urgency_recommendations(self, recommendations: List[str]) -> str:
        """Format urgency-specific recommendations"""

        output = """
🎯 **URGENCY-BASED RECOMMENDATIONS** 🎯

"""
        for rec in recommendations:
            output += f"• {rec}\n"

        return output.strip()

    def _format_adaptive_strategy(self, params: Dict) -> str:
        """Format adaptive strategy parameters"""

        return f"""
⚙️ **ADAPTIVE STRATEGY CONFIGURATION** ⚙️

🎪 **Strategy Focus**: {params['strategy_focus']}
⏱️ **Scan Frequency**: Every {params['scan_interval_seconds']} seconds
📊 **Max Trades**: {params['max_trades']}
🛡️ **Risk Per Trade**: {params['risk_per_trade']*100:.1f}%
📈 **Confidence Threshold**: {params['pattern_confidence_threshold']*100:.0f}%
⚡ **Urgency Multiplier**: {params['urgency_multiplier']}x

💡 **This configuration is AUTOMATICALLY optimized for your target timeframe!**
"""

    def _format_comprehensive_plan(self, plan: Dict, scan_results: List,
                                 sector_analysis: Dict, ai_insights: str) -> str:
        """Format comprehensive trading plan with Trump personality"""
        
        # Start with welcome message
        output = self.personality.generate_welcome_message() + "\n\n"
        
        # Add market analysis
        output += self.personality.format_market_analysis(plan['market_analysis'].__dict__) + "\n\n"
        
        # Add daily plan
        output += self.personality.format_daily_plan(plan) + "\n\n"
        
        # Add top opportunities
        if scan_results:
            output += self._format_top_opportunities(scan_results[:10]) + "\n\n"
        
        # Add sector analysis
        output += self._format_sector_analysis(sector_analysis) + "\n\n"
        
        # Add AI insights
        output += f"""
🤖 **ADVANCED AI MARKET ANALYSIS** 🤖

{ai_insights}

""" + "\n\n"
        
        # Add specific trading signals
        if plan.get('trading_signals'):
            output += self._format_trading_signals(plan['trading_signals']) + "\n\n"
        
        # Add execution strategy
        output += self._format_execution_strategy(plan.get('execution_strategy', {})) + "\n\n"
        
        # Add motivational close
        output += self.personality.get_motivational_message()
        
        return output

    def _format_top_opportunities(self, opportunities: List) -> str:
        """Format top market opportunities"""
        
        output = """
🎯 **TOP MARKET OPPORTUNITIES - HANDPICKED WINNERS!** 🎯

We've scanned the ENTIRE market and found these TREMENDOUS opportunities:

"""
        
        for i, opp in enumerate(opportunities[:5], 1):
            confidence_emoji = "🔥" if opp.score > 0.8 else "⭐" if opp.score > 0.7 else "👀"
            
            output += f"""
**{i}. {opp.symbol} - {opp.name[:30]}...** {confidence_emoji}
• **Price**: ${opp.price:.2f} ({opp.change_percent:+.2f}%)
• **Recommendation**: {opp.recommendation}
• **Score**: {opp.score:.2f}/1.0 (That's {opp.score*100:.0f}% FANTASTIC!)
• **Sector**: {opp.sector}
• **Volume**: {opp.volume:,} (vs avg)
• **Why it's GREAT**: {len(opp.patterns)} patterns detected!

"""
        
        output += """
💡 **STRATEGY**: These are our TOP picks for today. When we see our entry signals, we STRIKE with precision!
"""
        
        return output.strip()

    def _format_sector_analysis(self, sector_analysis: Dict) -> str:
        """Format sector rotation analysis"""
        
        monthly_leaders = sector_analysis.get('monthly_leaders', [])
        weekly_leaders = sector_analysis.get('weekly_leaders', [])
        
        output = f"""
🏭 **SECTOR ROTATION ANALYSIS - FOLLOW THE SMART MONEY!** 🏭

📈 **MONTHLY LEADERS** (Where the BIG money is flowing):
"""
        
        for i, sector in enumerate(monthly_leaders[:3], 1):
            output += f"{i}. {sector} - TREMENDOUS momentum!\n"
        
        output += f"""
⚡ **WEEKLY HOT SECTORS** (Short-term momentum):
"""
        
        for i, sector in enumerate(weekly_leaders[:3], 1):
            output += f"{i}. {sector} - Moving FAST!\n"
        
        output += """
💰 **SECTOR STRATEGY**: 
• Focus on stocks in LEADING sectors for momentum plays
• Watch for rotation opportunities in lagging sectors
• Sector ETFs can provide broad exposure to trends
"""
        
        return output.strip()

    def _format_trading_signals(self, signals: List[TradingSignal]) -> str:
        """Format specific trading signals"""
        
        if not signals:
            return """
🎯 **TRADING SIGNALS**

No PREMIUM signals right now, but we're watching like HAWKS! 
When the perfect setup appears, we'll POUNCE with TREMENDOUS force!
"""
        
        output = """
🎯 **SPECIFIC TRADING SIGNALS - READY TO EXECUTE!** 🎯

These are our BATTLE-TESTED signals for today:

"""
        
        for i, signal in enumerate(signals[:5], 1):
            risk_reward = signal.risk_reward_ratio
            expected_profit = signal.expected_profit
            
            output += f"""
**{i}. {signal.symbol} - {signal.action}** 🎯
• **Strategy**: {signal.strategy.value.upper()}
• **Entry**: ${signal.entry_price:.2f}
• **Target**: ${signal.target_price:.2f}
• **Stop Loss**: ${signal.stop_loss:.2f}
• **Position Size**: {signal.position_size} shares
• **Risk/Reward**: {risk_reward:.2f}:1 (FANTASTIC ratio!)
• **Expected Profit**: ${expected_profit:.2f}
• **Confidence**: {signal.confidence:.1%}
• **Timeframe**: {signal.timeframe}

**Why this is a WINNER**: {signal.reasoning.split('.')[0]}

"""
        
        return output.strip()

    def _format_execution_strategy(self, execution_strategy: Dict) -> str:
        """Format execution strategy"""
        
        if not execution_strategy:
            return ""
        
        output = """
⚡ **EXECUTION STRATEGY - HOW WE WIN TODAY!** ⚡

"""
        
        # Market open actions
        if execution_strategy.get('market_open_actions'):
            output += "🔔 **MARKET OPEN PLAN**:\n"
            for action in execution_strategy['market_open_actions']:
                output += f"• {action}\n"
            output += "\n"
        
        # Priority signals
        if execution_strategy.get('priority_signals'):
            output += "🎯 **PRIORITY TARGETS**:\n"
            for signal in execution_strategy['priority_signals'][:3]:
                output += f"• {signal['symbol']}: {signal['action']} at ${signal['entry']:.2f}\n"
            output += "\n"
        
        # Risk management
        if execution_strategy.get('risk_management'):
            rm = execution_strategy['risk_management']
            output += f"""
🛡️ **RISK MANAGEMENT** (We protect our capital like FORT KNOX!):
• Max risk per trade: {rm.get('max_risk_per_trade', 'Dynamic')}
• Daily loss limit: {rm.get('daily_loss_limit', 'Controlled')}
• Position sizing: {rm.get('position_sizing', 'Smart')}

"""
        
        # Timing
        if execution_strategy.get('timing_recommendations'):
            timing = execution_strategy['timing_recommendations']
            output += f"""
⏰ **OPTIMAL TIMING**:
• Best trading times: {timing.get('best_times', 'Market hours')}
• Times to avoid: {timing.get('avoid', 'Low volume periods')}
"""
        
        return output.strip()

    async def get_live_market_update(self) -> str:
        """Get live market update with current opportunities"""
        
        # Quick scan of top symbols
        quick_scan = await self.scanner.comprehensive_market_scan(max_symbols=25)
        
        # Get current market conditions
        market_analysis = await self.trading_engine.analyze_market_conditions()
        
        # Format update
        update = f"""
🚨 **LIVE MARKET UPDATE** 🚨
*{datetime.now().strftime('%H:%M:%S')} EST*

{self.personality.format_market_analysis(market_analysis.__dict__)}

🔥 **HOT OPPORTUNITIES RIGHT NOW**:
"""
        
        top_opportunities = [opp for opp in quick_scan if opp.score > 0.7][:3]
        
        if top_opportunities:
            for opp in top_opportunities:
                update += f"\n• **{opp.symbol}**: {opp.recommendation} (Score: {opp.score:.2f})"
        else:
            update += "\n• Scanning for new opportunities... Stay ALERT!"
        
        update += f"\n\n💰 **Daily Progress**: ${self.trading_engine.current_daily_pnl:.2f} / ${self.daily_profit_target:.2f}"
        
        return update

    async def analyze_specific_stock(self, symbol: str) -> str:
        """Provide detailed analysis of a specific stock"""
        
        # Analyze the symbol
        scan_result = await self.scanner._analyze_single_symbol(symbol.upper())
        
        if not scan_result:
            return f"❌ Unable to analyze {symbol.upper()}. Symbol may not be available or have insufficient data."
        
        # Get AI insights for this specific stock
        ai_analysis = await self._get_stock_specific_ai_analysis(scan_result)
        
        # Format with personality
        analysis = f"""
🎯 **DETAILED ANALYSIS: {scan_result.symbol}** 🎯

📊 **THE NUMBERS**:
• **Price**: ${scan_result.price:.2f} ({scan_result.change_percent:+.2f}%)
• **Market Cap**: ${scan_result.market_cap/1e9:.1f}B
• **Volume**: {scan_result.volume:,}
• **Sector**: {scan_result.sector}
• **Our Score**: {scan_result.score:.2f}/1.0

🎯 **RECOMMENDATION**: {scan_result.recommendation}

📈 **TECHNICAL METRICS**:
• **RSI**: {scan_result.key_metrics.get('rsi', 'N/A'):.1f}
• **Price vs 20-day avg**: {scan_result.key_metrics.get('price_vs_sma20', 0):+.1f}%
• **Volatility**: {scan_result.key_metrics.get('volatility', 0):.1f}%

🔍 **PATTERNS DETECTED**: {len(scan_result.patterns)}
"""
        
        # Add pattern details
        if scan_result.patterns:
            analysis += "\n🎪 **PATTERN BREAKDOWN**:\n"
            for pattern in scan_result.patterns:
                analysis += f"• {pattern.get('pattern_type', 'Unknown')}: {pattern.get('description', 'Pattern detected')}\n"
        
        # Add AI insights
        analysis += f"\n🤖 **AI ANALYSIS**:\n{ai_analysis}\n"
        
        # Add Trump-style conclusion
        if scan_result.score > 0.7:
            analysis += "\n💪 **BOTTOM LINE**: This is a TREMENDOUS opportunity! We're looking at a potential WINNER!"
        elif scan_result.score > 0.5:
            analysis += "\n👀 **BOTTOM LINE**: Decent setup, but we're looking for even BETTER opportunities!"
        else:
            analysis += "\n⚠️ **BOTTOM LINE**: Not our best opportunity right now. We'll find BETTER deals!"
        
        return analysis

    async def _get_stock_specific_ai_analysis(self, scan_result) -> str:
        """Get AI analysis for a specific stock"""
        
        try:
            prompt = f"""
Analyze {scan_result.symbol} with the following data:
- Price: ${scan_result.price:.2f} ({scan_result.change_percent:+.2f}%)
- Market Cap: ${scan_result.market_cap/1e9:.1f}B
- Sector: {scan_result.sector}
- Score: {scan_result.score:.2f}/1.0
- Patterns: {len(scan_result.patterns)} detected

Provide a brief analysis focusing on:
1. What the current setup suggests
2. Key levels to watch
3. Risk factors
4. Short-term outlook

Keep it concise and actionable.
"""
            
            headers = {
                "Authorization": f"Bearer {self.openai_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4",
                "messages": [
                    {"role": "system", "content": "You are a professional stock analyst providing concise, actionable insights."},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 400,
                "temperature": 0.7
            }
            
            response = requests.post(self.openai_url, headers=headers, json=data, timeout=20)
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return "AI analysis temporarily unavailable"
                
        except Exception as e:
            logger.error(f"Error getting stock-specific AI analysis: {e}")
            return "Technical analysis shows mixed signals. Monitor for clearer direction."
